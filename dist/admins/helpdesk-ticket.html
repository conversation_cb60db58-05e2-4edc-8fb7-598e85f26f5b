<!doctype html>
<html lang="en" class="preset-1" data-pc-sidebar-caption="true" data-pc-layout="vertical" data-pc-direction="ltr" dir="ltr" data-pc-theme_contrast="" data-pc-theme="light">
<!-- [Head] start -->

<head>
  <title>Helpdesk | Able Pro Dashboard Template</title>
  <!-- [Meta] -->
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta
    name="description"
    content="Able Pro is trending dashboard template made using Bootstrap 5 design framework. Able Pro is available in Bootstrap, React, CodeIgniter, Angular,  and .net Technologies."
  />
  <meta
    name="keywords"
    content="Bootstrap admin template, Dashboard UI Kit, Dashboard Template, Backend Panel, react dashboard, angular dashboard"
  />
  <meta name="author" content="Phoenixcoded" />

  <!-- [Favicon] icon -->
  <link rel="icon" href="../assets/images/favicon.svg" type="image/x-icon" />

  <link href="https://tutsplus.github.io/syntax-highlighter-demos/highlighters/highlightjs/styles/monokai_sublime.css"
    rel="stylesheet" />
  <link rel="stylesheet" href="../assets/css/plugins/quill.core.css" />
  <link rel="stylesheet" href="../assets/css/plugins/quill.snow.css" />
  <link rel="stylesheet" href="../assets/css/plugins/prism-coy.css" />
  <!-- [Font] Family -->
  <link rel="stylesheet" href="../assets/fonts/inter/inter.css" id="main-font-link" />
  <!-- [phosphor Icons] https://phosphoricons.com/ -->
  <link rel="stylesheet" href="../assets/fonts/phosphor/duotone/style.css" />
  <!-- [Tabler Icons] https://tablericons.com -->
  <link rel="stylesheet" href="../assets/fonts/tabler-icons.min.css" />
  <!-- [Feather Icons] https://feathericons.com -->
  <link rel="stylesheet" href="../assets/fonts/feather.css" />
  <!-- [Font Awesome Icons] https://fontawesome.com/icons -->
  <link rel="stylesheet" href="../assets/fonts/fontawesome.css" />
  <!-- [Material Icons] https://fonts.google.com/icons -->
  <link rel="stylesheet" href="../assets/fonts/material.css" />
  <!-- [Template CSS Files] -->
  <link rel="stylesheet" href="../assets/css/style.css" id="main-style-link" />

</head>
<!-- [Head] end -->
<!-- [Body] Start -->

<body>
  <!-- [ Pre-loader ] start -->
<div class="loader-bg fixed inset-0 bg-white dark:bg-themedark-cardbg z-[1034]">
  <div class="loader-track h-[5px] w-full inline-block absolute overflow-hidden top-0 bg-primary-500/10">
    <div class="loader-fill w-[300px] h-[5px] bg-primary-500 absolute top-0 left-0 transition-[transform_0.2s_linear] origin-left animate-[2.1s_cubic-bezier(0.65,0.815,0.735,0.395)_0s_infinite_normal_none_running_loader-animate]"></div>
  </div>
</div>
<!-- [ Pre-loader ] End -->
 <!-- [ Sidebar Menu ] start -->
<nav class="pc-sidebar">
  <div class="navbar-wrapper">
    <div class="m-header flex items-center py-4 px-6 h-header-height">
      <a href="../dashboard/index.html" class="b-brand flex items-center gap-3">
        <!-- ========   Change your logo from here   ============ -->
        <img src="../assets/images/logo-dark.svg" class="img-fluid logo-lg" alt="logo" />
        <span class="badge bg-success-500/10 text-success-500 rounded-full theme-version">v1.1.1</span>
      </a>
    </div>
    <div class="navbar-content h-[calc(100vh_-_74px)] py-2.5">
      <div class="card pc-user-card mx-[15px] mb-[15px] bg-theme-sidebaruserbg dark:bg-themedark-sidebaruserbg">
        <div class="card-body !p-5">
          <div class="flex items-center">
            <img class="shrink-0 w-[45px] h-[45px] rounded-full" src="../assets/images/user/avatar-1.jpg" alt="user-image" />
            <div class="ml-4 mr-2 grow">
              <h6 class="mb-0" data-i18n="Jonh Smith">Jonh Smith</h6>
              <small data-i18n="Administrator">Administrator</small>
            </div>
            <a class="shrink-0 btn btn-icon inline-flex btn-link-secondary" data-pc-toggle="collapse" href="#pc_sidebar_userlink">
              <svg class="pc-icon w-[22px] h-[22px]">
                <use xlink:href="#custom-sort-outline"></use>
              </svg>
            </a>
          </div>
          <div class="hidden pc-user-links" id="pc_sidebar_userlink">
            <div class="pt-3 *:flex *:items-center *:py-2 *:gap-2.5 hover:*:text-primary-500">
              <a href="#!">
                <i class="text-lg leading-none ti ti-user"></i>
                <span data-i18n="My Account">My Account</span>
              </a> 
              <a href="#!">
                <i class="text-lg leading-none ti ti-settings"></i>
                <span data-i18n="Settings">Settings</span>
              </a>
              <a href="#!">
                <i class="text-lg leading-none ti ti-lock"></i>
                <span data-i18n="Lock Screen">Lock Screen</span>
              </a>
              <a href="#!">
                <i class="text-lg leading-none ti ti-power"></i>
                <span data-i18n="Logout">Logout</span>
              </a>
            </div>
          </div>
        </div>
      </div>
      <ul class="pc-navbar">
        <li class="pc-item pc-caption">
          <label data-i18n="Navigation">Navigation</label>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-status-up"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Dashboard">Dashboard</span>
            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
            <span class="pc-badge">2</span>
          </a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../dashboard/index.html" data-i18n="Default">Default</a></li>
            <li class="pc-item"><a class="pc-link" href="../dashboard/analytics.html" data-i18n="Analytics">Analytics</a></li>
            <li class="pc-item"><a class="pc-link" href="../dashboard/finance.html" data-i18n="Finance">Finance</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-document"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Layouts">Layouts</span>
            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
          </a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../demo/layout-vertical.html" data-i18n="Vertical">Vertical</a></li>
            <li class="pc-item"><a class="pc-link" href="../demo/layout-horizontal.html" data-i18n="Horizontal">Horizontal</a></li>
            <li class="pc-item"><a class="pc-link" href="../demo/layout-color-header.html" data-i18n="Layouts 2">Layouts 2</a></li>
            <li class="pc-item"><a class="pc-link" href="../demo/layout-compact.html" data-i18n="Compact">Compact</a></li>
            <li class="pc-item"><a class="pc-link" href="../demo/layout-tab.html" data-i18n="Tab">Tab</a></li>
          </ul>
        </li>
        <li class="pc-item pc-caption">
          <label data-i18n="Widget">Widget</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-presentation-chart"></use>
          </svg>
        </li>
        <li class="pc-item">
          <a href="../widget/w_statistics.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-story"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Statistics">Statistics</span>
          </a>
        </li>
        <li class="pc-item">
          <a href="../widget/w_data.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-fatrows"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Data">Data</span>
          </a>
        </li>
        <li class="pc-item">
          <a href="../widget/w_chart.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-presentation-chart"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Chart">Chart</span></a
          >
        </li>
        <li class="pc-item pc-caption">
          <label data-i18n="Admin Panel">Admin Panel</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-layer"></use>
          </svg>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-layer"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Online Courses">Online Courses</span>
            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
          </a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../admins/course-dashboard.html" data-i18n="Dashboard">Dashboard</a></li>
            <li class="pc-item pc-hasmenu">
              <a class="pc-link" href="#!">
                <span data-i18n="Teacher">Teacher</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="../admins/course-teacher-list.html" data-i18n="List">List</a></li>
                <li class="pc-item"><a class="pc-link" href="../admins/course-teacher-apply.html" data-i18n="Apply">Apply</a></li>
                <li class="pc-item"><a class="pc-link" href="../admins/course-teacher-add.html" data-i18n="Add">Add</a></li>
              </ul>
            </li>
            <li class="pc-item pc-hasmenu">
              <a class="pc-link" href="#!">
                <span data-i18n="Student">Student</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="../admins/course-student-list.html" data-i18n="List">list</a></li>
                <li class="pc-item"><a class="pc-link" href="../admins/course-student-apply.html" data-i18n="Apply">Apply</a></li>
                <li class="pc-item"><a class="pc-link" href="../admins/course-student-add.html" data-i18n="Add">Add</a></li>
              </ul>
            </li>
            <li class="pc-item pc-hasmenu">
              <a class="pc-link" href="#!">
                <span data-i18n="Courses">Courses</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="../admins/course-course-view.html" data-i18n="View">View</a></li>
                <li class="pc-item"><a class="pc-link" href="../admins/course-course-add.html" data-i18n="Add">Add</a></li>
              </ul>
            </li>
            <li class="pc-item"><a class="pc-link" href="../admins/course-pricing.html" data-i18n="Pricing">Pricing</a></li>
            <li class="pc-item"><a class="pc-link" href="../admins/course-site.html" data-i18n="Site">Site</a></li>
            <li class="pc-item pc-hasmenu">
              <a class="pc-link" href="#!">
                <span data-i18n="Setting">Setting</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="../admins/course-setting-payment.html" data-i18n="Payment">Payment</a></li>
                <li class="pc-item"><a class="pc-link" href="../admins/course-setting-pricing.html" data-i18n="Pricing">Pricing</a></li>
                <li class="pc-item"><a class="pc-link" href="../admins/course-setting-notifications.html" data-i18n="Notification">Notifications</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-user"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Membership">Membership</span>
            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
          </a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../admins/membership-dashboard.html" data-i18n="Dashboard">Dashboard</a></li>
            <li class="pc-item"><a class="pc-link" href="../admins/membership-list.html" data-i18n="List">List</a></li>
            <li class="pc-item"><a class="pc-link" href="../admins/membership-pricing.html" data-i18n="Pricing">Pricing</a></li>
            <li class="pc-item"><a class="pc-link" href="../admins/membership-setting.html" data-i18n="Setting">Setting</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-24-support"></use>
              </svg> </span
            ><span class="pc-mtext" data-i18n="Helpdesk">Helpdesk</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../admins/helpdesk-dashboard.html" data-i18n="Dashboard">Dashboard</a></li>
            <li class="pc-item pc-hasmenu">
              <a class="pc-link" href="#!">
                <span data-i18n="Ticket">Ticket</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="../admins/helpdesk-create-ticket.html" data-i18n="Create">Create</a></li>
                <li class="pc-item"><a class="pc-link" href="../admins/helpdesk-ticket.html" data-i18n="List">List</a></li>
                <li class="pc-item"><a class="pc-link" href="../admins/helpdesk-ticket-details.html" data-i18n="Details">Details</a></li>
              </ul>
            </li>
            <li class="pc-item"><a class="pc-link" href="../admins/helpdesk-customer.html" data-i18n="Customer">Customer</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-bill"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Invoice">Invoice</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../admins/invoice-dashboard.html" data-i18n="Dashboard">Dashboard</a></li>
            <li class="pc-item"><a class="pc-link" href="../admins/invoice-create.html" data-i18n="Create">Create</a></li>
            <li class="pc-item"><a class="pc-link" href="../admins/invoice-view.html" data-i18n="Details">Details</a></li>
            <li class="pc-item"><a class="pc-link" href="../admins/invoice-list.html" data-i18n="List">List</a></li>
            <li class="pc-item"><a class="pc-link" href="../admins/invoice-edit.html" data-i18n="Edit">Edit</a></li>
          </ul>
        </li>
        <li class="pc-item pc-caption">
          <label data-i18n="UI Components">UI Components</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-box-1"></use>
          </svg>
        </li>
        <li class="pc-item">
          <a href="../elements/bc_alert.html" class="pc-link" target="_blank"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-box-1"></use>
              </svg> </span
            ><span class="pc-mtext" data-i18n="Components">Components</span></a
          >
        </li>
        <li class="pc-item">
          <a href="../elements/animation.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-flag"></use>
              </svg> </span
            ><span class="pc-mtext" data-i18n="Animation">Animation</span></a
          >
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-mouse-circle"></use>
              </svg> </span
            ><span class="pc-mtext" data-i18n="Icons">Icons</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../elements/icon-feather.html" data-i18n="Feather">Feather</a></li>
            <li class="pc-item"><a class="pc-link" href="../elements/icon-fontawesome.html" data-i18n="Font Awesome 5">Font Awesome 5</a></li>
            <li class="pc-item"><a class="pc-link" href="../elements/icon-material.html" data-i18n="Material">Material</a></li>
            <li class="pc-item"><a class="pc-link" href="../elements/icon-tabler.html" data-i18n="Tabler">Tabler</a></li>
            <li class="pc-item"><a class="pc-link" href="../elements/icon-phosphor.html" data-i18n="Phospher">Phosphor</a></li>
            <li class="pc-item"><a class="pc-link" href="../elements/icon-custom.html" data-i18n="Custom">Custom</a></li>
          </ul>
        </li>
        <li class="pc-item pc-caption">
          <label data-i18n="Forms">Forms</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-element-plus"></use>
          </svg>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-element-plus"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Form Elements">Forms Elements</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../forms/form_elements.html" data-i18n="Form Basic">Form Basic</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form_floating.html" data-i18n="Form Floating">Form Floating</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_basic.html" data-i18n="Form Options">Form Options</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_input_group.html" data-i18n="Input Group">Input Groups</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_checkbox.html" data-i18n="CheckBox">Checkbox</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_radio.html" data-i18n="Radio">Radio</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_switch.html" data-i18n="Switch">Switch</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_megaoption.html" data-i18n="Mega Option">Mega option</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-cpu-charge"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Form Plugins">Forms Plugins</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item pc-hasmenu">
              <a class="pc-link" href="#">
                <span data-i18n="Date">Date</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="../forms/form2_datepicker.html" data-i18n="Date Picker">Datepicker</a></li>
                <li class="pc-item"><a class="pc-link" href="../forms/form2_daterangepicker.html" data-i18n="Date Range Picker">Date Range Picker</a> </li>
                <li class="pc-item"><a class="pc-link" href="../forms/form2_timepicker.html" data-i18n="Timepicker">Timepicker</a></li>
              </ul>
            </li>
            <li class="pc-item pc-hasmenu">
              <a class="pc-link" href="#">
                <span data-i18n="Select">Select</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
              </a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="../forms/form2_choices.html" data-i18n="Choices js">Choices js</a></li>
              </ul>
            </li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_rating.html" data-i18n="Rating">Rating</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_recaptcha.html" data-i18n="Google-Re-Captcha">Google reCaptcha</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_inputmask.html" data-i18n="Input Mask">Input Masks</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_clipboard.html" data-i18n="ClipBoard">Clipboard</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_nouislider.html" data-i18n="Nouislider">Nouislider</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_switchjs.html" data-i18n="Bootstrap Switch">Bootstrap Switch</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_typeahead.html", data-i18n="TypeaHead">Typeahead</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-text-block"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Text Editor">Text Editors</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../forms/form2_tinymce.html" data-i18n="Tinymce">Tinymce</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_quill.html" data-i18n="Quill">Quill</a></li>
            <li class="pc-item pc-hasmenu">
              <a class="pc-link" href="#">
                <span data-i18n="CK editor">CK editor</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="../forms/editor-classic.html" data-i18n="classic">classic</a></li>
                <li class="pc-item"><a class="pc-link" href="../forms/editor-document.html" data-i18n="Document">Document</a></li>
                <li class="pc-item"><a class="pc-link" href="../forms/editor-inline.html" data-i18n="Inline">Inline</a></li>
                <li class="pc-item"><a class="pc-link" href="../forms/editor-balloon.html" data-i18n="Balloon">Balloon</a></li>
              </ul>
            </li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_markdown.html" data-i18n="Markdown">Markdown</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-row-vertical"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Form Layouts">Form Layouts</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../forms/form2_lay-default.html" data-i18n="Layouts">Layouts</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_lay-multicolumn.html" data-i18n="MultiColumn">Multicolumn</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_lay-actionbars.html" data-i18n="ActionBars">Actionbars</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_lay-stickyactionbars.html" data-i18n="Sticky-ActionBar">Sticky Action bars</a> </li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-document-upload"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Form Layouts">File upload</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../forms/file-upload.html" data-i18n="Dropzone">Dropzone</a></li>
            <li class="pc-item"><a class="pc-link" href="../forms/form2_flu-uppy.html" data-i18n="Uppy">Uppy</a></li>
          </ul>
        </li>
        <li class="pc-item">
          <a href="../forms/form2_wizard.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-password-check"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="wizard">Wizard</span></a
          >
        </li>
        <li class="pc-item">
          <a href="../forms/form-validation.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-kanban"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Form Validation">Form Validation</span></a
          >
        </li>
        <li class="pc-item"
          ><a href="../forms/image_crop.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-crop"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Images Cropper">Image cropper</span></a
          ></li
        >
        <li class="pc-item pc-caption">
          <label data-i18n="Tables">table</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-text-align-justify-center"></use>
          </svg>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-text-align-justify-center"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Bootstrap Table">Bootstrap Table</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../table/tbl_bootstrap.html" data-i18n="Basic Table">Basic table</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_sizing.html" data-i18n="Sizing table">Sizing table</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_border.html" data-i18n="Border table">Border table</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_styling.html" data-i18n="Styling table">Styling table</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-keyboard"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Vanilla table">Vanilla Table</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-simple.html" data-i18n="Basic initialization">Basic initialization</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-dynamic-import.html" data-i18n="Dynamic Import">Dynamic Import</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-render-column-cells.html" data-i18n="Render Column Cells">Render Column Cells</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-column-manipulation.html" data-i18n="Column Manipulation">Column Manipulation</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-datetime-sorting.html" data-i18n="Datetime Sorting">Datetime Sorting</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-methods.html" data-i18n="Methods">Methods</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-add-rows.html" data-i18n="Add Rows">Add Rows</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-fetch-api.html" data-i18n="Fetch API">Fetch API</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-filters.html" data-i18n="Filters">Filters</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/tbl_dt-export.html" data-i18n="Export">Export</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-graph"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Data Table">Data table</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../table/dt_advance.html" data-i18n="Advance initialization">Advance initialization</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_styling.html" data-i18n="Styling">Styling</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_api.html" data-i18n="API">API</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_plugin.html" data-i18n="Plug-in">Plug-in</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_sources.html" data-i18n="Data sources">Data sources</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-add-item"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="DT extensions">DT extensions</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../table/dt_ext_autofill.html" data-i18n="Autofill">Autofill</a></li>
            <li class="pc-item pc-hasmenu">
              <a href="#!" class="pc-link">
                <span data-i18n="Button">Button</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="../table/dt_ext_basic_buttons.html" data-i18n="Basic button">Basic button</a></li>
                <li class="pc-item"><a class="pc-link" href="../table/dt_ext_export_buttons.html" data-i18n="Data export">Data export</a></li>
              </ul>
            </li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_ext_col_reorder.html" data-i18n="Col reorder">Col reorder</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_ext_fixed_columns.html" data-i18n="Fixed columns">Fixed columns</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_ext_fixed_header.html" data-i18n="Fixed header">Fixed header</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_ext_key_table.html" data-i18n="Key table">Key table</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_ext_responsive.html" data-i18n="Responsive">Responsive</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_ext_row_reorder.html" data-i18n="Row reorder">Row reorder</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_ext_scroller.html" data-i18n="Scroller">Scroller</a></li>
            <li class="pc-item"><a class="pc-link" href="../table/dt_ext_select.html" data-i18n="Select">Select table</a></li>
          </ul>
        </li>

        <li class="pc-item pc-caption">
          <label data-i18n="Chart & Maps">Charts &and; Maps</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-graph"></use>
          </svg>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-graph"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Charts">Charts</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../chart_maps/chart-apex.html" data-i18n="Apex Chart">Apex Chart</a></li>
            <li class="pc-item"><a class="pc-link" href="../chart_maps/chart-peity.html" data-i18n="Peity Chart">Peity Chart</a></li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-shapes"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Map">Maps</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../chart_maps/map-vector.html" data-i18n="Vector Map">Vector Maps</a></li>
            <li class="pc-item"><a class="pc-link" href="../chart_maps/map-gmap.html" data-i18n="Google Map">Gmaps</a></li>
          </ul>
        </li>

        <li class="pc-item pc-caption">
          <label data-i18n="Application">Application</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-shopping-bag"></use>
          </svg>
        </li>
        <li class="pc-item">
          <a href="../application/calendar.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-calendar-1"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Calender">Calendar</span></a
          >
        </li>
        <li class="pc-item">
          <a href="../application/chat.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-message-2"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Chat">Chat</span></a
          >
        </li>
        <li class="pc-item">
          <a href="../application/cust_customer_list.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-notification-status"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Customer">Customer</span></a
          >
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-shopping-bag"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Ecommerce">E-commerce</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../application/ecom_product.html" data-i18n="Product">Product</a></li>
            <li class="pc-item"><a class="pc-link" href="../application/ecom_product-details.html" data-i18n="Product-Detail">Product details</a></li>
            <li class="pc-item"><a class="pc-link" href="../application/ecom_product-list.html" data-i18n="Product-List">Product List</a></li>
            <li class="pc-item"><a class="pc-link" href="../application/ecom_product-add.html" data-i18n="Product Add">Add New Product</a></li>
            <li class="pc-item"><a class="pc-link" href="../application/ecom_checkout.html" data-i18n="Checkout">Checkout</a></li>
          </ul>
        </li>
        <li class="pc-item">
          <a href="../application/file-manager.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-document-filter"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="File manager">File manager</span></a
          >
        </li>
        <li class="pc-item">
          <a href="../application/mail.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-direct-inbox"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Mail">Mail</span></a
          >
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-user-square"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="User">Users</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../application/account-profile.html" data-i18n="Account Profile">Account Profile</a></li>
            <li class="pc-item"><a class="pc-link" href="../application/social-media.html" data-i18n="Social Profile">Social media</a></li>
          </ul>
        </li>

        <li class="pc-item pc-caption">
          <label data-i18n="pages">Pages</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-flag"></use>
          </svg>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-shield"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Authentication">Authentication</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item pc-hasmenu">
              <a href="#!" class="pc-link">
                <span data-i18n="Authentication 1">Authentication 1</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/login-v1.html" data-i18n="Login">Login</a></li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/register-v1.html" data-i18n="Register">Register</a></li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/forgot-password-v1.html" data-i18n="Forget Password">Forgot Password</a></li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/check-mail-v1.html" data-i18n="Check Mail">check mail</a></li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/reset-password-v1.html" data-i18n="Reset Password">reset password</a> </li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/code-verification-v1.html" data-i18n="Code Verification">code verification</a></li>
              </ul>
            </li>
            <li class="pc-item pc-hasmenu">
              <a href="#!" class="pc-link">
                <span data-i18n="Authentication 2">Authentication 2</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/login-v2.html" data-i18n="Login">Login</a></li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/register-v2.html" data-i18n="Register">Register</a></li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/forgot-password-v2.html" data-i18n="Forget Password">Forgot password</a> </li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/check-mail-v2.html" data-i18n="Check Mail">check mail</a></li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/reset-password-v2.html" data-i18n="Reset Password">reset password</a> </li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/code-verification-v2.html" data-i18n="Code Verification">code verification</a></li>
              </ul>
            </li>
            <li class="pc-item">
              <a href="../pages/login-v3.html" target="_blank" class="pc-link" data-i18n="Authentication 3">Authentication 3</a>
            </li>
          </ul>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-flag"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Maintenance">Maintenance</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/error-404.html" data-i18n="Error 404">Error 404</a></li>
            <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/error-500.html" data-i18n="Error 500">Error 500</a></li>
            <li class="pc-item pc-hasmenu">
              <a href="#!" class="pc-link">
                <span data-i18n="Under construction">Under construction</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/under-construction-v1.html" data-i18n="Under construction 1">Under Construction 1</a></li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/under-construction-v2.html" data-i18n="Under construction 2">Under Construction 2</a></li>
              </ul>
            </li>
            <li class="pc-item pc-hasmenu">
              <a href="#!" class="pc-link">
                <span data-i18n="Coming-Soon">Coming soon</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/coming-soon-v1.html" data-i18n="Coming-Soon 1">Coming soon 1</a> </li>
                <li class="pc-item"><a class="pc-link" target="_blank" href="../pages/coming-soon-v2.html" data-i18n="Coming-Soon 2">Coming soon 2</a> </li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="pc-item"
          ><a href="../pages/contact-us.html" class="pc-link" target="_blank">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-24-support"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Contact Us">Contact us</span>
          </a>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-dollar-square"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Price">Price</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="../pages/price-v1.html" data-i18n="Price 1" data-i18n="Price 1">Price 1</a></li>
            <li class="pc-item"><a class="pc-link" href="../pages/price-v2.html" data-i18n="Price 2" data-i18n="Price 2">Price 2</a></li>
          </ul>
        </li>
        <li class="pc-item"
          ><a href="../index.html" class="pc-link" target="_blank"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-airplane"></use>
              </svg> </span
            ><span class="pc-mtext" data-i18n="Landing">Landing</span></a
          >
        </li>

        <li class="pc-item pc-caption">
          <label data-i18n="Other">Other</label>
          <svg class="pc-icon">
            <use xlink:href="#custom-notification-status"></use>
          </svg>
        </li>
        <li class="pc-item pc-hasmenu">
          <a href="#!" class="pc-link"
            ><span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-level"></use>
              </svg> </span
            ><span class="pc-mtext" data-i18n="Menu levels">Menu levels</span><span class="pc-arrow"><i data-feather="chevron-right"></i></span
          ></a>
          <ul class="pc-submenu">
            <li class="pc-item"><a class="pc-link" href="#!" data-i18n="Level 2.1">Level 2.1</a></li>
            <li class="pc-item pc-hasmenu">
              <a href="#!" class="pc-link">
                <span data-i18n="Level 2.2">Level 2.2</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="#!" data-i18n="Level 3.1">Level 3.1</a></li>
                <li class="pc-item"><a class="pc-link" href="#!" data-i18n="Level 3.2">Level 3.2</a></li>
                <li class="pc-item pc-hasmenu">
                  <a href="#!" class="pc-link">
                    <span data-i18n="Level 3.3">Level 3.3</span>
                    <span class="pc-arrow"><i data-feather="chevron-right"></i></span
                  ></a>
                  <ul class="pc-submenu">
                    <li class="pc-item"><a class="pc-link" href="#!" data-i18n="Level 4.1">Level 4.1</a></li>
                    <li class="pc-item"><a class="pc-link" href="#!" data-i18n="Level 4.2">Level 4.2</a></li>
                  </ul>
                </li>
              </ul>
            </li>
            <li class="pc-item pc-hasmenu">
              <a href="#!" class="pc-link">
                <span data-i18n="Level 2.3">Level 2.3</span>
                <span class="pc-arrow"><i data-feather="chevron-right"></i></span
              ></a>
              <ul class="pc-submenu">
                <li class="pc-item"><a class="pc-link" href="#!" data-i18n="Level 3.1">Level 3.1</a></li>
                <li class="pc-item"><a class="pc-link" href="#!" data-i18n="Level 3.2">Level 3.2</a></li>
                <li class="pc-item pc-hasmenu">
                  <a href="#!" class="pc-link">
                    <span data-i18n="Level 3.3">Level 3.3</span>
                    <span class="pc-arrow"><i data-feather="chevron-right"></i></span
                  ></a>
                  <ul class="pc-submenu">
                    <li class="pc-item"><a class="pc-link" href="#!" data-i18n="Level 4.1">Level 4.1</a></li>
                    <li class="pc-item"><a class="pc-link" href="#!" data-i18n="Level 4.2">Level 4.2</a></li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="pc-item"
          ><a href="../other/sample-page.html" class="pc-link">
            <span class="pc-micon">
              <svg class="pc-icon">
                <use xlink:href="#custom-notification-status"></use>
              </svg>
            </span>
            <span class="pc-mtext" data-i18n="Sample Page">Sample page</span></a
          ></li
        >

      </ul>
    </div>
  </div>
</nav>
<!-- [ Sidebar Menu ] end -->
 <!-- [ Header Topbar ] start -->
<header class="pc-header">
  <div class="header-wrapper flex max-sm:px-[15px] px-[25px] grow"><!-- [Mobile Media Block] start -->
<div class="me-auto pc-mob-drp">
  <ul class="inline-flex *:min-h-header-height *:inline-flex *:items-center">
    <!-- ======= Menu collapse Icon ===== -->
    <li class="pc-h-item pc-sidebar-collapse max-lg:hidden lg:inline-flex">
      <a href="#" class="pc-head-link ltr:!ml-0 rtl:!mr-0" id="sidebar-hide">
        <i class="ti ti-menu-2"></i>
      </a>
    </li>
    <li class="pc-h-item pc-sidebar-popup lg:hidden">
      <a href="#" class="pc-head-link ltr:!ml-0 rtl:!mr-0" id="mobile-collapse">
        <i class="ti ti-menu-2 text-2xl leading-none"></i>
      </a>
    </li>
    <li class="pc-h-item max-md:hidden md:inline-flex">
      <form class="form-search relative">
        <i class="search-icon absolute top-[14px] left-[15px]">
          <svg class="pc-icon w-4 h-4">
            <use xlink:href="#custom-search-normal-1"></use>
          </svg>
        </i>
        <input type="search" class="form-control px-2.5 pr-3 pl-10 w-[198px] leading-none" placeholder="Ctrl + K" />
      </form>
    </li>
  </ul>
</div>
<!-- [Mobile Media Block end] -->
<div class="ms-auto">
  <ul class="inline-flex *:min-h-header-height *:inline-flex *:items-center">
    <li class="dropdown pc-h-item">
      <a
        class="pc-head-link dropdown-toggle me-0"
        data-pc-toggle="dropdown"
        href="#"
        role="button"
        aria-haspopup="false"
        aria-expanded="false"
      >
        <svg class="pc-icon">
          <use xlink:href="#custom-sun-1"></use>
        </svg>
      </a>
      <div class="dropdown-menu dropdown-menu-end pc-h-dropdown">
        <a href="#!" class="dropdown-item" onclick="layout_change('dark')">
          <svg class="pc-icon w-[18px] h-[18px]">
            <use xlink:href="#custom-moon"></use>
          </svg>
          <span>Dark</span>
        </a>
        <a href="#!" class="dropdown-item" onclick="layout_change('light')">
          <svg class="pc-icon w-[18px] h-[18px]">
            <use xlink:href="#custom-sun-1"></use>
          </svg>
          <span>Light</span>
        </a>
        <a href="#!" class="dropdown-item" onclick="layout_change_default()">
          <svg class="pc-icon w-[18px] h-[18px]">
            <use xlink:href="#custom-setting-2"></use>
          </svg>
          <span>Default</span>
        </a>
      </div>
    </li>
    <li class="dropdown pc-h-item">
      <a
        class="pc-head-link dropdown-toggle me-0"
        data-pc-toggle="dropdown"
        href="#"
        role="button"
        aria-haspopup="false"
        aria-expanded="false"
      >
        <svg class="pc-icon">
          <use xlink:href="#custom-language"></use>
        </svg>
      </a>
      <div class="dropdown-menu dropdown-menu-end pc-h-dropdown lng-dropdown">
        <a href="#!" class="dropdown-item" data-lng="en">
          <span>
            English
            <small>(UK)</small>
          </span>
        </a>
        <a href="#!" class="dropdown-item" data-lng="fr">
          <span>
            français
            <small>(French)</small>
          </span>
        </a>
        <a href="#!" class="dropdown-item" data-lng="ro">
          <span>
            Română
            <small>(Romanian)</small>
          </span>
        </a>
        <a href="#!" class="dropdown-item" data-lng="cn">
          <span>
            中国人
            <small>(Chinese)</small>
          </span>
        </a>
      </div>
    </li>
    <li class="dropdown pc-h-item">
      <a
        class="pc-head-link dropdown-toggle arrow-none me-0"
        data-bs-toggle="dropdown"
        href="#"
        role="button"
        aria-haspopup="false"
        aria-expanded="false"
      >
        <svg class="pc-icon">
          <use xlink:href="#custom-setting-2"></use>
        </svg>
      </a>
      <div class="dropdown-menu dropdown-menu-end pc-h-dropdown">
        <a href="#!" class="dropdown-item">
          <i class="ti ti-user"></i>
          <span>My Account</span>
        </a>
        <a href="#!" class="dropdown-item">
          <i class="ti ti-settings"></i>
          <span>Settings</span>
        </a>
        <a href="#!" class="dropdown-item">
          <i class="ti ti-headset"></i>
          <span>Support</span>
        </a>
        <a href="#!" class="dropdown-item">
          <i class="ti ti-lock"></i>
          <span>Lock Screen</span>
        </a>
        <a href="#!" class="dropdown-item">
          <i class="ti ti-power"></i>
          <span>Logout</span>
        </a>
      </div>
    </li>
    <li class="pc-h-item">
      <a href="#" class="pc-head-link me-0" data-pc-toggle="offcanvas" data-pc-target="#announcement" aria-controls="announcement">
        <svg class="pc-icon">
          <use xlink:href="#custom-flash"></use>
        </svg>
      </a>
    </li>
    <li class="dropdown pc-h-item">
      <a
        class="pc-head-link dropdown-toggle me-0"
        data-pc-toggle="dropdown"
        href="#"
        role="button"
        aria-haspopup="false"
        aria-expanded="false"
      >
        <svg class="pc-icon">
          <use xlink:href="#custom-notification"></use>
        </svg>
        <span class="badge bg-success-500 text-white rounded-full z-10 absolute right-0 top-0">3</span>
      </a>
      <div class="dropdown-menu dropdown-notification dropdown-menu-end pc-h-dropdown p-2">
        <div class="dropdown-header flex items-center justify-between py-4 px-5">
          <h5 class="m-0">Notifications</h5>
          <a href="#!" class="btn btn-link btn-sm">Mark all read</a>
        </div>
        <div class="dropdown-body header-notification-scroll relative py-4 px-5" style="max-height: calc(100vh - 215px)">
          <p class="text-span mb-3">Today</p>
          <div class="card mb-2">
            <div class="card-body">
              <div class="flex gap-4">
                <div class="shrink-0">
                  <svg class="pc-icon text-primary w-[22px] h-[22px]">
                    <use xlink:href="#custom-layer"></use>
                  </svg>
                </div>
                <div class="grow">
                  <span class="float-end text-sm text-muted">2 min ago</span>
                  <h5 class="text-body mb-2">UI/UX Design</h5>
                  <p class="mb-0">
                    Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
                    type and scrambled it to make a type
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="card mb-2">
            <div class="card-body">
              <div class="flex gap-4">
                <div class="shrink-0">
                  <svg class="pc-icon text-primary w-[22px] h-[22px]">
                    <use xlink:href="#custom-sms"></use>
                  </svg>
                </div>
                <div class="grow">
                  <span class="float-end text-sm text-muted">1 hour ago</span>
                  <h5 class="text-body mb-2">Message</h5>
                  <p class="mb-0">Lorem Ipsum has been the industry's standard dummy text ever since the 1500.</p>
                </div>
              </div>
            </div>
          </div>
          <p class="text-span mb-3 mt-4">Yesterday</p>
          <div class="card mb-2">
            <div class="card-body">
              <div class="flex gap-4">
                <div class="shrink-0">
                  <svg class="pc-icon text-primary w-[22px] h-[22px]">
                    <use xlink:href="#custom-document-text"></use>
                  </svg>
                </div>
                <div class="grow ms-3">
                  <span class="float-end text-sm text-muted">2 hour ago</span>
                  <h5 class="text-body mb-2">Forms</h5>
                  <p class="mb-0">
                    Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
                    type and scrambled it to make a type
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="card mb-2">
            <div class="card-body">
              <div class="flex gap-4">
                <div class="shrink-0">
                  <svg class="pc-icon text-primary w-[22px] h-[22px]">
                    <use xlink:href="#custom-user-bold"></use>
                  </svg>
                </div>
                <div class="grow ms-3">
                  <span class="float-end text-sm text-muted">12 hour ago</span>
                  <h5 class="text-body mb-2">Challenge invitation</h5>
                  <p class="mb-2">
                    <span class="text-dark">Jonny aber</span>
                    invites to join the challenge
                  </p>
                  <button class="btn btn-sm btn-outline-secondary me-2">Decline</button>
                  <button class="btn btn-sm btn-primary">Accept</button>
                </div>
              </div>
            </div>
          </div>
          <div class="card mb-2">
            <div class="card-body">
              <div class="flex gap-4">
                <div class="shrink-0">
                  <svg class="pc-icon text-primary w-[22px] h-[22px]">
                    <use xlink:href="#custom-security-safe"></use>
                  </svg>
                </div>
                <div class="grow ms-3">
                  <span class="float-end text-sm text-muted">5 hour ago</span>
                  <h5 class="text-body mb-2">Security</h5>
                  <p class="mb-0">
                    Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
                    type and scrambled it to make a type
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="text-center py-2">
          <a href="#!" class="text-danger-500 hover:text-danger-600 focus:text-danger-600 active:text-danger-600">
            Clear all Notifications
          </a>
        </div>
      </div>
    </li>
    <li class="dropdown pc-h-item header-user-profile">
      <a
        class="pc-head-link dropdown-toggle arrow-none me-0"
        data-pc-toggle="dropdown"
        href="#"
        role="button"
        aria-haspopup="false"
        data-pc-auto-close="outside"
        aria-expanded="false"
      >
        <img src="../assets/images/user/avatar-2.jpg" alt="user-image" class="user-avtar w-10 h-10 rounded-full" />
      </a>
      <div class="dropdown-menu dropdown-user-profile dropdown-menu-end pc-h-dropdown p-2">
        <div class="dropdown-header flex items-center justify-between py-4 px-5">
          <h5 class="m-0">Profile</h5>
        </div>
        <div class="dropdown-body py-4 px-5">
          <div class="profile-notification-scroll position-relative" style="max-height: calc(100vh - 225px)">
            <div class="flex mb-1 items-center">
              <div class="shrink-0">
                <img src="../assets/images/user/avatar-2.jpg" alt="user-image" class="w-10 rounded-full" />
              </div>
              <div class="grow ms-3">
                <h6 class="mb-1">Carson Darrin 🖖</h6>
                <span><EMAIL></span>
              </div>
            </div>
            <hr class="border-secondary-500/10 my-4" />
            <div class="card">
              <div class="card-body !py-4">
                <div class="flex items-center justify-between">
                  <h5 class="mb-0 inline-flex items-center">
                    <svg class="pc-icon text-muted me-2 w-[22px] h-[22px]">
                      <use xlink:href="#custom-notification-outline"></use>
                    </svg>
                    Notification
                  </h5>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" value="" class="sr-only peer" />
                    <div
                      class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"
                    ></div>
                  </label>
                </div>
              </div>
            </div>
            <p class="text-span mb-3">Manage</p>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2 inline-block">
                  <use xlink:href="#custom-setting-outline"></use>
                </svg>
                <span>Settings</span>
              </span>
            </a>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2 inline-block">
                  <use xlink:href="#custom-share-bold"></use>
                </svg>
                <span>Share</span>
              </span>
            </a>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2 inline-block">
                  <use xlink:href="#custom-lock-outline"></use>
                </svg>
                <span>Change Password</span>
              </span>
            </a>
            <hr class="border-secondary-500/10 my-4" />
            <p class="text-span mb-3">Team</p>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2 inline-block">
                  <use xlink:href="#custom-profile-2user-outline"></use>
                </svg>
                <span>UI Design team</span>
              </span>
              <div dir="ltr"
                class="flex -space-x-2 overflow-hidden *:flex *:items-center *:justify-center *:rounded-full *:w-[30px] *:h-[30px] hover:*:z-10 *:border *:border-2 *:border-white"
              >
                <img src="../assets/images/user/avatar-1.jpg" alt="user-image" class="avtar" />
                <span class="avtar bg-danger text-white">K</span>
                <span class="avtar bg-success text-white">
                  <svg class="pc-icon m-0">
                    <use xlink:href="#custom-user"></use>
                  </svg>
                </span>
                <span class="avtar bg-theme-cardbg dark:bg-themedark-cardbg overflow-hidden">
                  <span class="flex items-center justify-center w-full h-full bg-primary-500/10 text-primary-500">+2</span>
                </span>
              </div>
            </a>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2 inline-block">
                  <use xlink:href="#custom-profile-2user-outline"></use>
                </svg>
                <span>Friends Groups</span>
              </span>
              <div dir="ltr"
                class="flex -space-x-2 overflow-hidden *:flex *:items-center *:justify-center *:rounded-full *:w-[30px] *:h-[30px] hover:*:z-10 *:border *:border-2 *:border-white"
              >
                <img src="../assets/images/user/avatar-1.jpg" alt="user-image" class="avtar" />
                <span class="avtar bg-danger text-white">K</span>
                <span class="avtar bg-success text-white">
                  <svg class="pc-icon m-0">
                    <use xlink:href="#custom-user"></use>
                  </svg>
                </span>
              </div>
            </a>
            <a href="#" class="dropdown-item">
              <span>
                <svg class="pc-icon text-muted me-2 inline-block">
                  <use xlink:href="#custom-add-outline"></use>
                </svg>
                <span>Add new</span>
              </span>
              <div dir="ltr"
                class="flex -space-x-2 overflow-hidden *:flex *:items-center *:justify-center *:rounded-full *:w-[30px] *:h-[30px] hover:*:z-10 *:border-2 *:border-white"
              >
                <span class="avtar bg-primary text-white">
                  <svg class="pc-icon m-0">
                    <use xlink:href="#custom-add-outline"></use>
                  </svg>
                </span>
              </div>
            </a>
            <hr class="border-secondary-500/10 my-4" />
            <div class="grid mb-3">
              <button class="btn btn-primary flex items-center justify-center">
                <svg class="pc-icon me-2 w-[22px] h-[22px]">
                  <use xlink:href="#custom-logout-1-outline"></use>
                </svg>
                Logout
              </button>
            </div>
            <div
              class="card border-0 shadow-none drp-upgrade-card mb-0 bg-cover"
              style="background-image: url(../assets/images/layout/img-profile-card.jpg)"
            >
              <div class="card-body">
                <div
                  class="flex -space-x-3 overflow-hidden *:flex *:items-center *:justify-center *:rounded-full *:w-10 *:h-10 hover:*:z-10 *:border-2 *:border-white"
                >
                  <img src="../assets/images/user/avatar-1.jpg" alt="user-image" class="avtar" />
                  <img src="../assets/images/user/avatar-2.jpg" alt="user-image" class="avtar" />
                  <img src="../assets/images/user/avatar-3.jpg" alt="user-image" class="avtar" />
                  <img src="../assets/images/user/avatar-4.jpg" alt="user-image" class="avtar" />
                  <img src="../assets/images/user/avatar-5.jpg" alt="user-image" class="avtar" />
                  <span class="avtar bg-theme-cardbg dark:bg-themedark-cardbg overflow-hidden">
                    <span class="flex items-center justify-center w-full h-full bg-primary-500/10 text-primary-500">+20</span>
                  </span>
                </div>
                <h3 class="my-4 text-dark">
                  245.3k
                  <small class="text-muted">Followers</small>
                </h3>
                <div class="btn btn-warning inline-flex items-center justify-center">
                  <svg class="pc-icon me-2 w-[22px] h-[22px]">
                    <use xlink:href="#custom-logout-1-outline"></use>
                  </svg>
                  Upgrade to Business
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ul>
</div>
</div>
</header>
<div class="offcanvas pc-announcement-offcanvas offcanvas-end" tcabindex="-1" id="announcement" aria-labelledby="announcementLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="announcementLabel">What's new announcement?</h5>
    <button
      data-pc-dismiss="#announcement"
      class="text-lg flex items-center justify-center rounded w-7 h-7 text-secondary-500 hover:bg-danger-500/10 hover:text-danger-500"
    >
      <i class="ti ti-x"></i>
    </button>
  </div>
  <div class="offcanvas-body announcement-scroll-block">
    <p class="mb-3">Today</p>
    <div class="card mb-3">
      <div class="card-body">
        <div class="items-center flex wrap gap-2 mb-3">
          <div class="badge text-success-500 bg-success-500/10 text-sm">Big News</div>
          <p class="mb-0">2 min ago</p>
          <span class="badge bg-warning-500 p-1"></span>
        </div>
        <h5 class="mb-3">Able Pro is Redesigned</h5>
        <p class="text-muted mb-3">Able Pro is completely renowed with high aesthetics User Interface.</p>
        <img src="../assets/images/layout/img-announcement-1.png" alt="img" class="img-fluid mb-3" />
        <div class="grid">
          <a class="btn btn-outline-secondary" href="https://1.envato.market/zNkqj6" target="_blank">Check Now</a>
        </div>
      </div>
    </div>
    <div class="card mb-3">
      <div class="card-body">
        <div class="items-center flex wrap gap-2 mb-3">
          <div class="badge text-warning-500 bg-warning-500/10 text-sm">Offer</div>
          <p class="mb-0 text-muted">2 hour ago</p>
          <span class="badge bg-warning p-1"></span>
        </div>
        <h5 class="mb-3">Able Pro is in best offer price</h5>
        <p class="text-muted mb-3">Download Able Pro exclusive on themeforest with best price.</p>
        <a href="https://1.envato.market/zNkqj6" target="_blank">
          <img src="../assets/images/layout/img-announcement-2.png" alt="img" class="img-fluid" />
        </a>
      </div>
    </div>

    <p class="text-span mb-3 mt-4">Yesterday</p>
    <div class="card mb-3">
      <div class="card-body">
        <div class="items-center flex wrap gap-2 mb-3">
          <div class="badge text-primary-500 bg-primary-500/10 text-sm">Blog</div>
          <p class="mb-0 text-muted">12 hour ago</p>
          <span class="badge bg-warning p-1"></span>
        </div>
        <h5 class="mb-3">Featured Dashboard Template</h5>
        <p class="text-muted mb-3">Do you know Able Pro is one of the featured dashboard template selected by Themeforest team.?</p>
        <img src="../assets/images/layout/img-announcement-3.png" alt="img" class="img-fluid" />
      </div>
    </div>
    <div class="card mb-3">
      <div class="card-body">
        <div class="items-center flex wrap gap-2 mb-3">
          <div class="badge text-primary-500 bg-primary-500/10 text-sm">Announcement</div>
          <p class="mb-0 text-muted">12 hour ago</p>
          <span class="badge bg-warning p-1"></span>
        </div>
        <h5 class="mb-3">Buy Once - Get Free Updated lifetime</h5>
        <p class="text-muted mb-3">Get the lifetime free updates once you purchase the Able Pro.</p>
        <img src="../assets/images/layout/img-announcement-4.png" alt="img" class="img-fluid" />
      </div>
    </div>
  </div>
</div>
<!-- [ Header ] end -->



  <!-- [ Main Content ] start -->
  <div class="pc-container">
    <div class="pc-content">
      <!-- [ breadcrumb ] start -->
      <div class="page-header">
        <div class="page-block">
          <ul class="breadcrumb">
            <li class="breadcrumb-item"><a href="../dashboard/index.html">Home</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0)">Helpdesk</a></li>
            <li class="breadcrumb-item" aria-current="page">Ticket list</li>
          </ul>
          <div class="page-header-title">
            <h2 class="mb-0">Ticket list</h2>
          </div>
        </div>
      </div>
      <!-- [ breadcrumb ] end -->


      <!-- [ Main Content ] start -->
      <div class="grid grid-cols-12 gap-x-6">
        <div class="col-span-12 xl:col-span-8 help-main group">
          <div class="card">
            <div class="card-body">
              <nav class="flex  justify-between p-0 items-center">
                <h5>Ticket List</h5>
                <div class="inline-flex items-center help-filter" role="group">
                  <label
                    class="btn btn-sm btn-light-secondary cursor-pointer ltr:rounded-r-none rtl:rounded-l-none has-[:checked]:bg-secondary-500 has-[:checked]:text-white"
                    onclick='filterchange("sm")' for="btnrdolite1">
                    <input type="radio" class="forced-colors:appearance-auto appearance-none" id="btnrdolite1"
                      name="btn_radio2" /><i class="feather icon-align-justify m-0"></i></label>
                  <label
                    class="btn btn-sm btn-light-secondary cursor-pointer rounded-none has-[:checked]:bg-secondary-500 has-[:checked]:text-white"
                    onclick='filterchange("md")' for="btnrdolite2">
                    <input type="radio" class="forced-colors:appearance-auto appearance-none" id="btnrdolite2"
                      name="btn_radio2" /><i class="feather icon-menu m-0"></i></label>
                  <label
                    class="btn btn-sm btn-light-secondary cursor-pointer ltr:rounded-l-none rtl:rounded-r-none has-[:checked]:bg-secondary-500 has-[:checked]:text-white"
                    onclick='filterchange("large")' for="btnrdolite3">
                    <input type="radio" class="forced-colors:appearance-auto appearance-none" id="btnrdolite3"
                      name="btn_radio2" checked /><i class="feather icon-grid m-0"></i></label>
                </div>
              </nav>
            </div>
          </div>
          <div class="card ticket-card">
            <div class="card-body">
              <div class="flex max-sm:flex-col items-start gap-6">
                <div class="shrink-0 mr-3">
                  <div class="d-sm-inline-block d-flex align-items-center">
                    <img class="media-object w-[60px] rounded-full" src="../assets/images/user/avatar-1.jpg"
                      alt="Generic placeholder image " />
                    <div class="ml-3 sm:ml-0">
                      <ul class="sm:text-center list-unstyled mt-2 ml-0 mb-0 inline-block">
                        <li class="list-unstyled-item"><a href="#" class="text-secondary-500">1 Ticket</a></li>
                        <li class="list-unstyled-item"><a href="#" class="text-danger-500"><i class="fas fa-heart"></i>
                            3</a></li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="grow">
                  <div class="popup-trigger">
                    <div class="h5 font-weight-bold">John lui <small
                        class="badge bg-secondary-500/10 text-secondary-500 dark:text-white ms-2">Replied</small></div>
                    <div class="group-[.sm-view]:hidden">
                      <ul class="flex flex-wrap mt-2 mb-0 text-muted gap-2">
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><img src="../assets/images/admin/p1.jpg"
                            alt="" class="w-5 rounded" />Piaf able</li>
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><img
                            src="../assets/images/user/avatar-5.jpg" alt="" class="w-5 rounded" />Assigned to
                          <b>Robert alia</b>
                        </li>
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><i
                            class="w-5 material-icons-two-tone text-center text-base">calendar_today</i>Updated 22 hours
                          ago</li>
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><i
                            class="w-5 material-icons-two-tone text-center text-base">chat</i>9
                        </li>
                      </ul>
                    </div>
                    <div class="h5 mt-3"><i class="material-icons-two-tone text-[16px] mr-1">lock</i> Theme
                      customisation issue</div>
                    <div class="group-[.md-view]:hidden group-[.sm-view]:hidden">
                      <div class="bg-theme-bodybg dark:bg-themedark-bodybg rounded-lg mb-3 p-4">
                        <h6 class="mb-2">
                          <img src="../assets/images/user/avatar-5.jpg" alt=""
                            class="w-5 mr-2 rounded-md inline-block" />
                          Last comment from
                          <a href="#" class="text-muted">Robert alia:</a>
                        </h6>
                        <p class="mb-0">
                          <b>hello John lui</b>,<br />
                          you need to create <b>"toolbar-options" div only</b> once in a page&nbsp;in your code,<br />
                          this div fill found every "td" tag in your page,<br />
                          just remove those things and also in option button add
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="mt-2">
                    <a href="helpdesk-ticket-details.html" class="mr-2 btn btn-sm btn-light-primary"><i
                        class="feather icon-eye mx-1"></i>View Ticket</a>
                    <a href="#" class="mr-3 btn btn-sm btn-light-danger"><i
                        class="feather icon-trash-2 mx-1"></i>Delete</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card ticket-card border-l-4 border-l-danger-500">
            <div class="card-body">
              <div class="flex max-sm:flex-col items-start gap-6">
                <div class="shrink-0 mr-3">
                  <div class="d-sm-inline-block d-flex align-items-center">
                    <img class="media-object w-[60px] rounded-full" src="../assets/images/user/avatar-1.jpg"
                      alt="Generic placeholder image " />
                    <div class="ml-3 sm:ml-0">
                      <ul class="sm:text-center list-unstyled mt-2 ml-0 mb-0 inline-block">
                        <li class="list-unstyled-item"><a href="#" class="text-secondary-500">1 Ticket</a></li>
                        <li class="list-unstyled-item"><a href="#" class="text-danger-500"><i class="fas fa-heart"></i>
                            3</a></li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="grow">
                  <div class="popup-trigger">
                    <div class="h5 font-weight-bold">John lui <small
                        class="badge bg-secondary-500/10 text-secondary-500 dark:text-white ms-2">Replied</small></div>
                    <div class="group-[.sm-view]:hidden">
                      <ul class="flex flex-wrap mt-2 mb-0 text-muted gap-2">
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><img src="../assets/images/admin/p1.jpg"
                            alt="" class="w-5 rounded" />Piaf able</li>
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><img
                            src="../assets/images/user/avatar-5.jpg" alt="" class="w-5 rounded" />Assigned to
                          <b>Robert alia</b>
                        </li>
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><i
                            class="w-5 material-icons-two-tone text-center text-base">calendar_today</i>Updated 22 hours
                          ago</li>
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><i
                            class="w-5 material-icons-two-tone text-center text-base">chat</i>9
                        </li>
                      </ul>
                    </div>
                    <div class="h5 mt-3"><i class="material-icons-two-tone text-[16px] mr-1">lock</i> Theme
                      customisation issue</div>
                    <div class="group-[.md-view]:hidden group-[.sm-view]:hidden">
                      <div class="bg-theme-bodybg dark:bg-themedark-bodybg rounded-lg mb-3 p-4">
                        <h6 class="mb-2">
                          <img src="../assets/images/user/avatar-5.jpg" alt=""
                            class="w-5 mr-2 rounded-md inline-block" />
                          Last comment from
                          <a href="#" class="text-muted">Robert alia:</a>
                        </h6>
                        <p class="mb-0">
                          <b>hello John lui</b>,<br />
                          you need to create <b>"toolbar-options" div only</b> once in a page&nbsp;in your code,<br />
                          this div fill found every "td" tag in your page,<br />
                          just remove those things and also in option button add
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="mt-2">
                    <a href="helpdesk-ticket-details.html" class="mr-2 btn btn-sm btn-light-primary"><i
                        class="feather icon-eye mx-1"></i>View Ticket</a>
                    <a href="#" class="mr-3 btn btn-sm btn-light-danger"><i
                        class="feather icon-trash-2 mx-1"></i>Delete</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card ticket-card border-l-4 border-l-success-500">
            <div class="card-body">
              <div class="flex max-sm:flex-col items-start gap-6">
                <div class="shrink-0 mr-3">
                  <div class="d-sm-inline-block d-flex align-items-center">
                    <img class="media-object w-[60px] rounded-full" src="../assets/images/user/avatar-1.jpg"
                      alt="Generic placeholder image " />
                    <div class="ml-3 sm:ml-0">
                      <ul class="sm:text-center list-unstyled mt-2 ml-0 mb-0 inline-block">
                        <li class="list-unstyled-item"><a href="#" class="text-secondary-500">1 Ticket</a></li>
                        <li class="list-unstyled-item"><a href="#" class="text-danger-500"><i class="fas fa-heart"></i>
                            3</a></li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="grow">
                  <div class="popup-trigger">
                    <div class="h5 font-weight-bold">John lui <small
                        class="badge bg-secondary-500/10 text-secondary-500 dark:text-white ms-2">Replied</small></div>
                    <div class="group-[.sm-view]:hidden">
                      <ul class="flex flex-wrap mt-2 mb-0 text-muted gap-2">
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><img src="../assets/images/admin/p1.jpg"
                            alt="" class="w-5 rounded" />Piaf able</li>
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><img
                            src="../assets/images/user/avatar-5.jpg" alt="" class="w-5 rounded" />Assigned to
                          <b>Robert alia</b>
                        </li>
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><i
                            class="w-5 material-icons-two-tone text-center text-base">calendar_today</i>Updated 22 hours
                          ago</li>
                        <li class="sm:inline-flex items-center flex gap-1 mt-1"><i
                            class="w-5 material-icons-two-tone text-center text-base">chat</i>9
                        </li>
                      </ul>
                    </div>
                    <div class="h5 mt-3"><i class="material-icons-two-tone text-[16px] mr-1">lock</i> Theme
                      customisation issue</div>
                    <div class="group-[.md-view]:hidden group-[.sm-view]:hidden">
                      <div class="bg-theme-bodybg dark:bg-themedark-bodybg rounded-lg mb-3 p-4">
                        <h6 class="mb-2">
                          <img src="../assets/images/user/avatar-5.jpg" alt=""
                            class="w-5 mr-2 rounded-md inline-block" />
                          Last comment from
                          <a href="#" class="text-muted">Robert alia:</a>
                        </h6>
                        <p class="mb-0">
                          <b>hello John lui</b>,<br />
                          you need to create <b>"toolbar-options" div only</b> once in a page&nbsp;in your code,<br />
                          this div fill found every "td" tag in your page,<br />
                          just remove those things and also in option button add
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="mt-2">
                    <a href="helpdesk-ticket-details.html" class="mr-2 btn btn-sm btn-light-primary"><i
                        class="feather icon-eye mx-1"></i>View Ticket</a>
                    <a href="#" class="mr-3 btn btn-sm btn-light-danger"><i
                        class="feather icon-trash-2 mx-1"></i>Delete</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-span-12 xl:col-span-4">
          <div class="right-side">
            <div class="card mb-3">
              <div class="card-header">
                <h5>Ticket Categories</h5>
              </div>
              <ul
                class="rounded-lg *:py-4 *:px-[25px] divide-y divide-inherit border-theme-border dark:border-themedark-border">
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/admin/p1.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">Piaf able</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#" class="badge bg-danger-500/10 text-danger-500 rounded-full mr-1">1</a>
                    <a href="#"
                      class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0">3</a>
                  </div>
                </li>
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/admin/p2.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">Pro able</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#"
                      class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0">3</a>
                  </div>
                </li>
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/admin/p3.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">CRM admin</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#" class="badge bg-danger-500/10 text-danger-500 rounded-full mr-1">1</a>
                    <a href="#"
                      class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0">3</a>
                  </div>
                </li>
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/admin/p4.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">Alpha pro</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#"
                      class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0">3</a>
                  </div>
                </li>
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/admin/p5.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">Carbon able</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#"
                      class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0">3</a>
                  </div>
                </li>
              </ul>
            </div>
            <div class="card">
              <div class="card-header pt-4 pb-4">
                <h5>Support Aggent</h5>
              </div>
              <ul
                class="rounded-lg *:py-4 *:px-[25px] divide-y divide-inherit border-theme-border dark:border-themedark-border">
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/user/avatar-5.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">Tom Cook</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#" class="badge bg-danger-500/10 text-danger-500 rounded-full mr-1"
                      data-pc-toggle="tooltip" data-bs-placement="top" title=""
                      data-original-title="tooltip on top">1</a>
                    <a href="#" class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0"
                      data-pc-toggle="tooltip" data-bs-placement="top" title=""
                      data-original-title="tooltip on top">3</a>
                  </div>
                </li>
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/user/avatar-4.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">Brad Larry</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#" class="badge bg-danger-500/10 text-danger-500 rounded-full mr-1"
                      data-pc-toggle="tooltip" data-bs-placement="top" title=""
                      data-original-title="tooltip on top">1</a>
                    <a href="#" class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0"
                      data-pc-toggle="tooltip" data-bs-placement="top" title=""
                      data-original-title="tooltip on top">3</a>
                  </div>
                </li>
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/user/avatar-3.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">Jhon White</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#" class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0"
                      data-pc-toggle="tooltip" data-bs-placement="top" title=""
                      data-original-title="tooltip on top">3</a>
                  </div>
                </li>
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/user/avatar-2.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">Mark Jobs</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#" class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0"
                      data-pc-toggle="tooltip" data-bs-placement="top" title=""
                      data-original-title="tooltip on top">3</a>
                  </div>
                </li>
                <li class="list-group-item">
                  <div class="inline-block">
                    <img src="../assets/images/user/avatar-1.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />
                    <a href="#">Able Pro</a>
                  </div>
                  <div class="ltr:float-right rtl:float-left">
                    <a href="#" class="badge bg-secondary-500/10 text-secondary-500 dark:text-white rounded-full mr-0"
                      data-pc-toggle="tooltip" data-bs-placement="top" title=""
                      data-original-title="tooltip on top">3</a>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <!-- [ Main Content ] end -->
    </div>
  </div>

  <div class="offcanvas group offcanvas-end !w-[550px] [&:not(.show)]:right-[-550px] has-[.show]:right-0" tabindex="-1"
    id="qviewModal" aria-labelledby="qviewModalLabel">
    <div class="offcanvas-header ">
      <h5 class="modal-title grow me-2">
        Chrome bug The page uses a roller to slide under a black block
        <span class="badge bg-danger-500/10 text-danger-500 uppercase ml-2">Private</span>
      </h5>
      <div class="shrink-0">
        <button data-pc-dismiss="#qviewModal"
          class="text-lg flex items-center justify-center rounded w-7 h-7 text-secondary-500 hover:bg-danger-500/10 hover:text-danger-500">
          <i class="ti ti-x"></i>
        </button>
      </div>
    </div>
    <div class="scroll-div position-relative" style="height: calc(100vh - 90px)">
      <div class="offcanvas-body border-y border-theme-border dark:border-themedark-border px-4">
        <div class="grid grid-cols-12 gap-6">
          <div class="col-span-12 md:col-span-7">
            <span class="badge bg-success-500/10 text-success-500 mr-1"><i class="feather icon-check mr-1"></i>
              Closed</span>
            <p class="mb-0 inline-block">
              <img src="../assets/images/admin/p1.jpg" alt="" class="w-5 rounded-lg ltr:mr-1 rtl:ml-1 inline-block" />Alpha pro
            </p>
          </div>
          <div class="col-span-12 md:col-span-5 text-right">
            <p class="inline-block mb-0"><i
                class="w-5 material-icons-two-tone text-center align-text-top text-[16px] mr-2">calendar_today</i><label
                class="mb-0">Jan,1st,2019</label></p>
          </div>
        </div>
      </div>
      <div class="offcanvas-body border-y border-theme-border dark:border-themedark-border px-4">
        <div class="flex items-start">
          <div class="shrink-0 ltr:mr-3 rtl:ml-3">
            <img class="w-[60px] rounded-full" src="../assets/images/user/avatar-5.jpg"
              alt="Generic placeholder image " />
          </div>
          <div class="grow">
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
              <strong>Note!</strong> This ticket is closed. If you want to re-open it, just post a reply below.
              <button data-pc-dismiss="alert"
                class="text-lg flex items-center justify-center rounded w-7 h-7 text-inherit bg-transparent hover:bg-inherit bg-opacity-10">
                <i class="ti ti-x"></i>
              </button>
            </div>
            <div id="tinymce-editor" style="height: 150px">
              <p>hello..</p>
            </div>
            <div class="btn-block mt-3">
              <div class="btn-group inline-flex items-center me-2">
                <button type="button" class="btn btn-sm btn-light-primary rounded-r-none">Primary</button>
                <button type="button"
                  class="btn btn-sm btn-light-primary rounded-l-none dropdown-toggle dropdown-toggle-split"
                  data-pc-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="#!">Action</a>
                  <a class="dropdown-item" href="#!">Another action</a>
                  <a class="dropdown-item" href="#!">Something else here</a>
                  <div class="dropdown-divider"></div>
                  <a class="dropdown-item" href="#!">Separated link</a>
                </div>
              </div>
              <label class="btn btn-sm btn-light-secondary" for="mod-flup">
                <i class="feather icon-paperclip mr-1"></i> Add Atachment
              </label>
              <input type="file" name="file" class="hidden" id="mod-flup" />
            </div>
          </div>
        </div>
      </div>
      <div class="offcanvas-body border-y border-theme-border dark:border-themedark-border px-4">
        <div class="flex items-start">
          <div class="shrink-0 ltr:mr-3 rtl:ml-3">
            <img class="w-[60px] rounded-full" src="../assets/images/user/avatar-5.jpg"
              alt="Generic placeholder image " />
          </div>
          <div class="grow">
            <h6 class="mb-0">Support Agent name<span
                class="badge bg-secondary-500/10 text-secondary-500 dark:text-white ms-2">Support Agent</span></h6>
            <label class="text-muted">5 Month ago</label>
          </div>
          <div class="shrink-0 ml-3">
            <ul class="flex items-center gap-1 mb-0">
              <li><a href="#"><i class="feather icon-edit text-muted"></i></a></li>
              <li><a href="#"><i class="feather icon-trash-2 text-muted"></i></a></li>
            </ul>
          </div>
        </div>
        <div class="mt-3 mb-4">
          <p>hello John lui,</p>
          <p>you need to create <strong>"toolbar-options" div only once</strong> in a page in your code, this div fill
            found
            <strong>every "td"</strong> tag in your page, just remove those things.
          </p>
          <p>and also</p>
          <p>in option button add "<strong>p-0</strong>" class in "<strong>I</strong>" tag</p>
          <p>to</p>
          <p></p>
          <p>Thanks...</p>
        </div>
        <div class="code-toolbar">
          <pre class="before:!hidden after:!hidden language-markup ">
<code class=" language-markup">
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>pre</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>code</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>language-css<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    p {
        color: #1abc9c
    }
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>code</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>pre</span><span class="token punctuation">&gt;</span></span>
</code>
                    </pre>
        </div>
      </div>
      <div class="offcanvas-body border-y border-theme-border dark:border-themedark-border px-4">
        <div class="flex items-start">
          <div class="shrink-0 ltr:mr-3 rtl:ml-3">
            <img class="w-[60px] rounded-full" src="../assets/images/user/avatar-1.jpg"
              alt="Generic placeholder image " />
          </div>
          <div class="grow">
            <h6 class="mb-0">Support Agent name<span
                class="badge bg-secondary-500/10 text-secondary-500 dark:text-white ms-2">Support Agent</span></h6>
            <label class="text-muted">5 Month ago</label>
          </div>
          <div class="shrink-0 ml-3">
            <ul class="flex items-center gap-1 mb-0">
              <li><a href="#"><i class="feather icon-edit text-muted"></i></a></li>
              <li><a href="#"><i class="feather icon-trash-2 text-muted"></i></a></li>
            </ul>
          </div>
        </div>
        <div class="mt-3 mb-4">
          <p>hello John lui,</p>
          <p>you need to create <strong>"toolbar-options" div only once</strong> in a page in your code, this div fill
            found
            <strong>every "td"</strong> tag in your page, just remove those things.
          </p>
          <p>and also</p>
          <p>in option button add "<strong>p-0</strong>" class in "<strong>I</strong>" tag</p>
          <p>to</p>
          <p></p>
          <p>Thanks...</p>
        </div>
        <div class="grid grid-cols-12 gap-2 text-center mb-2">
          <div class="xl:col-span-2 lg:col-span-3 sm:col-span-4 col-span-6">
            <img src="../assets/images/light-box/sl1.jpg" class="w-full rounded-md" alt="" />
          </div>
          <div class="xl:col-span-2 lg:col-span-3 sm:col-span-4 col-span-6">
            <img src="../assets/images/light-box/sl2.jpg" class="w-full rounded-md" alt="" />
          </div>
          <div class="xl:col-span-2 lg:col-span-3 sm:col-span-4 col-span-6">
            <img src="../assets/images/light-box/sl5.jpg" class="w-full rounded-md" alt="" />
          </div>
          <div class="xl:col-span-2 lg:col-span-3 sm:col-span-4 col-span-6">
            <img src="../assets/images/light-box/sl6.jpg" class="w-full rounded-md" alt="" />
          </div>
          <div class="xl:col-span-2 lg:col-span-3 sm:col-span-4 col-span-6">
            <img src="../assets/images/light-box/sl1.jpg" class="w-full rounded-md" alt="" />
          </div>
        </div>
      </div>
      <div class="offcanvas-body border-y border-theme-border dark:border-themedark-border px-4">
        <div class="flex items-start">
          <div class="shrink-0 ltr:mr-3 rtl:ml-3">
            <img class="w-[60px] rounded-full" src="../assets/images/user/avatar-5.jpg"
              alt="Generic placeholder image " />
          </div>
          <div class="grow">
            <h6 class="mb-0">Support Agent name<span
                class="badge bg-secondary-500/10 text-secondary-500 dark:text-white ms-2">Support Agent</span></h6>
            <label class="text-muted">5 Month ago</label>
          </div>
          <div class="shrink-0 ml-3">
            <ul class="flex items-center gap-1 mb-0">
              <li><a href="#"><i class="feather icon-edit text-muted"></i></a></li>
              <li><a href="#"><i class="feather icon-trash-2 text-muted"></i></a></li>
            </ul>
          </div>
        </div>
        <div class="mt-3 mb-4">
          <p>hello John lui,</p>
          <p>you need to create <strong>"toolbar-options" div only once</strong> in a page in your code, this div fill
            found
            <strong>every "td"</strong> tag in your page, just remove those things.
          </p>
          <p>and also</p>
          <p>in option button add "<strong>p-0</strong>" class in "<strong>I</strong>" tag</p>
          <p>to</p>
          <p></p>
          <p>Thanks...</p>
        </div>
        <div class="code-toolbar">
          <pre class="before:!hidden after:!hidden language-markup ">
<code class=" language-markup">
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>pre</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>code</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>language-css<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    p {
        color: #1abc9c
    }
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>code</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>pre</span><span class="token punctuation">&gt;</span></span>
</code>
                    </pre>
        </div>
      </div>
    </div>
  </div>
  <!-- [ Main Content ] end -->
  <footer class="pc-footer">
    <div class="footer-wrapper container-fluid mx-10">
      <div class="grid grid-cols-12 gap-1.5">
        <div class="col-span-12 sm:col-span-6 my-1">
          <p class="m-0">
            © Able Pro crafted with ♥ by Team <a href="https://themeforest.net/user/phoenixcoded" class="text-theme-bodycolor dark:text-themedark-bodycolor hover:text-primary-500 dark:hover:text-primary-500" target="_blank">Phoenixcoded</a>
          </p>
        </div>
        <div class="col-span-12 sm:col-span-6 my-1">
          <ul class="mb-0 ltr:sm:text-right rtl:sm:text-left *:text-theme-bodycolor dark:*:text-themedark-bodycolor hover:*:text-primary-500 dark:hover:*:text-primary-500">
            <li class="inline-block max-sm:mr-2 sm:ml-2"><a href="../index.html">Home</a></li>
            <li class="inline-block max-sm:mr-2 sm:ml-2"><a href="https://phoenixcoded.gitbook.io/able-pro/" target="_blank">Documentation</a></li>
            <li class="inline-block max-sm:mr-2 sm:ml-2"><a href="https://phoenixcoded.authordesk.app/" target="_blank">Support</a></li>
          </ul>
        </div>
      </div>
    </div>
  </footer>
 <!-- Required Js -->
<script src="../assets/js/plugins/simplebar.min.js"></script>
<script src="../assets/js/plugins/popper.min.js"></script>
<script src="../assets/js/plugins/i18next.min.js"></script>
<script src="../assets/js/plugins/i18nextHttpBackend.min.js"></script>
<script src="../assets/js/icon/custom-font.js"></script>
<script src="../assets/js/plugins/feather.min.js"></script>
<script src="../assets/js/component.js"></script>
<script src="../assets/js/theme.js"></script>
<script src="../assets/js/multi-lang.js"></script>
<script src="../assets/js/script.js"></script>
<div class="floting-button fixed bottom-[50px] right-[30px] z-[1030]">
  <a href="https://1.envato.market/zNkqj6" class="btn btn-danger buynowlinks animate-[btn-floating_2s_infinite] max-sm:p-[13px] max-sm:rounded-full inline-flex items-center gap-2" data-pc-toggle="tooltip" data-pc-title="Buy Now">
    <i class="ph-duotone ph-shopping-cart text-lg leading-none"></i>
    <span class="hidden sm:inline-block">Buy Now</span>
  </a>
</div>


<script>
  layout_change('false');
</script>
 
<script>
  layout_theme_contrast_change('false');
</script>
 
<script>
  change_box_container('false');
</script>
 
<script>
  layout_caption_change('true');
</script>
 
<script>
  layout_rtl_change('false');
</script>
 
<script>
  preset_change('preset-1');
</script>
 
<script>
  main_layout_change('vertical');
</script>


  <!-- [Page Specific JS] start -->
  <!-- prism Js -->
  <script src="../assets/js/plugins/prism.js"></script>
  <script src="../assets/js/plugins/quill.js"></script>

  <script>

    const helpMain = document.querySelector('.help-main');
    const qviewpopup = document.getElementById('qviewModal');
    const popupTriggers = document.querySelectorAll('.popup-trigger');
    const helpFilterLinks = document.querySelectorAll('.help-filter a');
    const scrollDiv = document.querySelector('.scroll-div');

    function filterchange(tempnum) {
      helpMain.classList.remove('sm-view', 'md-view', 'large-view');
      helpMain.classList.add(`${tempnum}-view`);
    }

    // Initialize Quill editor
    new Quill('#tinymce-editor', {
      modules: {
        toolbar: [
          [{ header: [1, 2, false] }],
          ['bold', 'italic', 'underline'],
          ['image', 'code-block']
        ]
      },
      placeholder: 'Type your text here...',
      theme: 'snow'
    });

    // Popup triggers
    popupTriggers.forEach(trigger => {
      trigger.addEventListener('click', () => {
        const backDropOverlay = document.createElement('div');
        backDropOverlay.className = 'fixed inset-0 bg-gray-900/20 z-[1027] backdrop-blur-sm';
        backDropOverlay.id = 'offcanvasoverlay';
        document.body.appendChild(backDropOverlay);
        backDropOverlay.addEventListener('click', offcanvasclose);
        qviewpopup.classList.add('show');
      });
    });

    // Initialize custom scrollbar if needed
    if (scrollDiv) {
      new SimpleBar(scrollDiv);
    }

    // Help filter click events
    helpFilterLinks.forEach(link => {
      link.addEventListener('click', (event) => {
        const activeLink = document.querySelector('.help-filter a.active');
        if (activeLink) {
          activeLink.classList.remove('active');
        }

        let targetElement = event.target;
        if (targetElement.tagName === 'I') {
          targetElement = targetElement.parentNode;
        }

        targetElement.classList.add('active');
      });
    });

  </script>
  <!-- [Page Specific JS] end -->
  <div class="pct-c-btn block fixed ltr:-right-1 rtl:-left-1 top-[100px] z-[1030] overflow-hidden p-0 border-4 ltr:border-r-0 rtl:border-l-0 ltr:rounded-[50%_4px_4px_50%] rtl:rounded-[4px_50%_50%_4px] shadow-[-6px_0px_14px_1px_rgba(27,46,94,0.04)] border-theme-cardbg dark:border-themedark-cardbg bg-theme-cardbg dark:bg-themedark-cardbg transition-all">
    <a href="#" class="block py-3 px-4 transition-all hover:bg-primary-500/10" data-pc-toggle="offcanvas" data-pc-target="#offcanvas_pc_layout">
      <i class="ph-duotone ph-gear-six block text-[24px] leading-none text-primary-500"></i>
    </a>
  </div>
  <div class="offcanvas pct-offcanvas !w-[320px] offcanvas-end !z-[1031]" tabindex="-1" id="offcanvas_pc_layout" aria-labelledby="offcanvas_pc_layoutLabel">
    <div class="offcanvas-header">
      <h5 id="offcanvas_pc_layoutLabel">Settings</h5>
      <button data-pc-dismiss="#offcanvas_pc_layout" class="text-lg flex items-center justify-center rounded w-7 h-7 text-secondary-500 hover:bg-danger-500/10 hover:text-danger-500">
          <i class="ti ti-x"></i>
      </button>
    </div>
    <div class="offcanvas-body pct-body !px-[25px] !pt-0 h-[calc(100%_-_85px)]">
      <ul class="rounded-lg *:py-4 divide-y divide-inherit border-theme-border dark:border-themedark-border">
        <li class="list-group-item">
          <div class="pc-dark">
            <h6 class="mb-1">Theme Mode</h6>
            <p class="text-muted text-sm mb-4">Choose light or dark mode or Auto</p>
            <div class="grid grid-cols-12 gap-6 theme-color theme-layout">
              <div class="col-span-4">
                <div class="grid">
                  <button
                    class="preset-btn btn active"
                    data-value="true"
                    onclick="layout_change('light');"
                  >
                    <svg class="pc-icon text-warning-500 w-[22px] h-[22px]">
                      <use xlink:href="#custom-sun-1"></use>
                    </svg>
                  </button>
                </div>
              </div>
              <div class="col-span-4">
                <div class="grid">
                  <button class="preset-btn btn" data-value="false" onclick="layout_change('dark');">
                    <svg class="pc-icon w-[22px] h-[22px]">
                      <use xlink:href="#custom-moon"></use>
                    </svg>
                  </button>
                </div>
              </div>
              <div class="col-span-4">
                <div class="grid">
                  <button
                    class="preset-btn btn"
                    data-value="default"
                    onclick="layout_change_default();"
                  >
                    <span class="pc-lay-icon d-flex align-items-center justify-content-center">
                      <i class="ph-duotone ph-cpu text-[26px]"></i>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </li>
      
        <li class="list-group-item">
          <h6 class="mb-1">Theme Contrast</h6>
          <p class="text-muted text-sm mb-4">Choose theme contrast</p>
          <div class="grid grid-cols-12 gap-6 theme-contrast">
            <div class="col-span-6">
              <div class="grid">
                <button
                  class="preset-btn btn"
                  data-value="true"
                  onclick="layout_theme_contrast_change('true');"
                >
                  <svg class="pc-icon w-[22px] h-[22px]">
                    <use xlink:href="#custom-mask"></use>
                  </svg>
                </button>
              </div>
            </div>
            <div class="col-span-6">
              <div class="grid">
                <button
                  class="preset-btn btn active"
                  data-value="false"
                  onclick="layout_theme_contrast_change('false');"
                >
                  <svg class="pc-icon w-[22px] h-[22px]">
                    <use xlink:href="#custom-mask-1-outline"></use>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </li>
        <li class="list-group-item">
          <h6 class="mb-1">Custom Theme</h6>
          <p class="text-muted text-sm mb-4">Choose your primary theme color</p>
          <div class="flex items-center flex-wrap gap-1 mt-3 theme-color preset-color">
            <a href="#" class="group bg-primary-500 active" data-value="preset-1">
              <i class="block group-[.active]:hidden ph-duotone ph-arrow-counter-clockwise text-white text-lg leading-none"></i>
              <i class="hidden group-[.active]:block ph-duotone ph-paint-brush text-white text-lg leading-none"></i>
            </a>
            <a href="#" class="bg-red-500" data-value="preset-2"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-orange-500" data-value="preset-3"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-amber-500" data-value="preset-4"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-yellow-500" data-value="preset-5"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-lime-500" data-value="preset-6"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-green-500" data-value="preset-7"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-emerald-500" data-value="preset-8"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-teal-500" data-value="preset-9"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-cyan-500" data-value="preset-10"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-sky-500" data-value="preset-11"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-blue-500" data-value="preset-12"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-indigo-500" data-value="preset-13"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-violet-500" data-value="preset-14"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-purple-500" data-value="preset-15"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-fuchsia-500" data-value="preset-16"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-pink-500" data-value="preset-17"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
            <a href="#" class="bg-rose-500" data-value="preset-18"><i class="ph-duotone ph-paint-brush text-white text-lg leading-none"></i></a>
          </div>
        </li>
        <li class="list-group-item">
          <h6 class="mb-1">Theme layout</h6>
          <p class="text-muted text-sm mb-4">Choose your layout</p>
          <div class="theme-main-layout flex items-center gap-1 w-full">
            <a href="#!" class="preset-btn btn btn-img active" data-value="vertical">
              <img src="../assets/images/customizer/caption-on.svg" alt="img" class="img-fluid" />
            </a>
            <a href="#!" class="preset-btn btn btn-img" data-value="horizontal">
              <img src="../assets/images/customizer/horizontal.svg" alt="img" class="img-fluid" />
            </a>
            <a href="#!" class="preset-btn btn btn-img" data-value="color-header">
              <img src="../assets/images/customizer/color-header.svg" alt="img" class="img-fluid" />
            </a>
            <a href="#!" class="preset-btn btn btn-img" data-value="compact">
              <img src="../assets/images/customizer/compact.svg" alt="img" class="img-fluid" />
            </a>
            <a href="#!" class="preset-btn btn btn-img" data-value="tab">
              <img src="../assets/images/customizer/tab.svg" alt="img" class="img-fluid" />
            </a>
          </div>
        </li>
        <li class="list-group-item">
          <h6 class="mb-1">Sidebar Caption</h6>
          <p class="text-muted text-sm mb-4">Sidebar Caption Hide/Show</p>
          <div class="grid grid-cols-12 gap-6 theme-color theme-nav-caption">
            <div class="col-span-6">
              <div class="grid">
                <button
                  class="preset-btn btn-img btn active"
                  data-value="true"
                  onclick="layout_caption_change('true');"
                >
                  <img src="../assets/images/customizer/caption-on.svg" alt="img" class="img-fluid" />
                </button>
              </div>
            </div>
            <div class="col-span-6">
              <div class="grid">
                <button
                  class="preset-btn btn-img btn"
                  data-value="false"
                  onclick="layout_caption_change('false');"
                >
                  <img src="../assets/images/customizer/caption-off.svg" alt="img" class="img-fluid" />
                </button>
              </div>
            </div>
          </div>
        </li>
        <li class="list-group-item">
          <div class="pc-rtl">
            <h6 class="mb-1">Theme Layout</h6>
            <p class="text-muted text-sm mb-4">LTR/RTL</p>
            <div class="grid grid-cols-12 gap-6 theme-color theme-direction">
              <div class="col-span-6">
                <div class="grid">
                  <button
                    class="preset-btn btn-img btn active"
                    data-value="false"
                    onclick="layout_rtl_change('false');"
                  >
                    <img src="../assets/images/customizer/ltr.svg" alt="img" class="img-fluid" />
                  </button>
                </div>
              </div>
              <div class="col-span-6">
                <div class="grid">
                  <button
                    class="preset-btn btn-img btn"
                    data-value="true"
                    onclick="layout_rtl_change('true');"
                  >
                    <img src="../assets/images/customizer/rtl.svg" alt="img" class="img-fluid" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </li>
        <li class="list-group-item pc-box-width">
          <div class="pc-container-width">
            <h6 class="mb-1">Layout Width</h6>
            <p class="text-muted text-sm mb-4">Choose Full or Container Layout</p>
            <div class="grid grid-cols-12 gap-6 theme-color theme-container">
              <div class="col-span-6">
                <div class="grid">
                  <button
                    class="preset-btn btn-img btn active"
                    data-value="false"
                    onclick="change_box_container('false')"
                  >
                    <img src="../assets/images/customizer/full.svg" alt="img" class="img-fluid" />
                  </button>
                </div>
              </div>
              <div class="col-span-6">
                <div class="grid">
                  <button
                    class="preset-btn btn-img btn"
                    data-value="true"
                    onclick="change_box_container('true')"
                  >
                    <img src="../assets/images/customizer/fixed.svg" alt="img" class="img-fluid" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </li>
        <li class="list-group-item">
          <div class="grid">
            <button class="btn btn-light-danger" id="layoutreset">Reset Layout</button>
          </div>
        </li>
      </ul>
    </div>
  </div>

</body>
<!-- [Body] end -->

</html>