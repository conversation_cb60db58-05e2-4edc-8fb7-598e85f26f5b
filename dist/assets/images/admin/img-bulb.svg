<svg width="120" height="118" viewBox="0 0 120 118" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<rect x="0.5" y="0.5" width="119" height="108" fill="url(#pattern0)"/>
<path d="M60.5 14.5V7.5" stroke="url(#paint0_linear_7249_34935)" stroke-width="4" stroke-linecap="round"/>
<path d="M77.4999 19.6242L81.5 12.5" stroke="url(#paint1_linear_7249_34935)" stroke-width="4" stroke-linecap="round"/>
<path d="M89.3174 30.4453L96.5 26.5" stroke="url(#paint2_linear_7249_34935)" stroke-width="4" stroke-linecap="round"/>
<path d="M94.5 46.5L102.5 46.5" stroke="url(#paint3_linear_7249_34935)" stroke-width="4" stroke-linecap="round"/>
<path d="M45.5001 19.6242L41.5 12.5" stroke="url(#paint4_linear_7249_34935)" stroke-width="4" stroke-linecap="round"/>
<path d="M33.6826 30.4453L26.5 26.5" stroke="url(#paint5_linear_7249_34935)" stroke-width="4" stroke-linecap="round"/>
<path d="M28.5 46.5L20.5 46.5" stroke="url(#paint6_linear_7249_34935)" stroke-width="4" stroke-linecap="round"/>
<g filter="url(#filter0_f_7249_34935)">
<circle cx="62" cy="46" r="16.5" fill="#FFE9B5" fill-opacity="0.6"/>
</g>
<ellipse cx="62" cy="111.5" rx="24.5" ry="6" fill="url(#paint7_radial_7249_34935)" fill-opacity="0.16"/>
<defs>
<pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_7249_34935" transform="scale(0.00840336 0.00925926)"/>
</pattern>
<filter id="filter0_f_7249_34935" x="31.5" y="15.5" width="61" height="61" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="7" result="effect1_foregroundBlur_7249_34935"/>
</filter>
<linearGradient id="paint0_linear_7249_34935" x1="57" y1="7.5" x2="59.5676" y2="22.9054" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC846"/>
<stop offset="1" stop-color="#E9A501"/>
</linearGradient>
<linearGradient id="paint1_linear_7249_34935" x1="81.2034" y1="12.3288" x2="85.7622" y2="19.2702" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC846"/>
<stop offset="1" stop-color="#E9A501"/>
</linearGradient>
<linearGradient id="paint2_linear_7249_34935" x1="94.815" y1="23.4323" x2="80.8418" y2="35.0825" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC846"/>
<stop offset="1" stop-color="#E9A501"/>
</linearGradient>
<linearGradient id="paint3_linear_7249_34935" x1="102.5" y1="43" x2="85.0383" y2="46.326" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC846"/>
<stop offset="1" stop-color="#E9A501"/>
</linearGradient>
<linearGradient id="paint4_linear_7249_34935" x1="41.7966" y1="12.3288" x2="37.2378" y2="19.2702" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC846"/>
<stop offset="1" stop-color="#E9A501"/>
</linearGradient>
<linearGradient id="paint5_linear_7249_34935" x1="28.185" y1="23.4323" x2="42.1582" y2="35.0825" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC846"/>
<stop offset="1" stop-color="#E9A501"/>
</linearGradient>
<linearGradient id="paint6_linear_7249_34935" x1="20.5" y1="43" x2="37.9617" y2="46.326" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC846"/>
<stop offset="1" stop-color="#E9A501"/>
</linearGradient>
<radialGradient id="paint7_radial_7249_34935" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(62 111.5) rotate(90) scale(6 24.5)">
<stop/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
<image id="image0_7249_34935" width="128" height="128" xlink:href="data:image/png;base64,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"/>
</defs>
</svg>
