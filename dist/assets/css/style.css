/**======================================================================
=========================================================================
Template Name: Able Pro - Tailwind Admin Template
Author: Phoenixcoded
Support: https://phoenixcoded.authordesk.app
File: style.css
=========================================================================
=================================================================================== */
@import url("plugins/simplebar.min.css");
/*! tailwindcss v3.4.10 | MIT License | https://tailwindcss.com */
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}
::before,
::after {
  --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: "Inter var", sans-serif; /* 4 */
  font-feature-settings: "salt"; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
  color: inherit;
  text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}
/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
  cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}
:root{
  --colors-primary-50: 227 236 255;
  --colors-primary-100: 200 217 255;
  --colors-primary-200: 163 192 255;
  --colors-primary-300: 126 166 255;
  --colors-primary-400: 98 147 255;
  --colors-primary-500: 70 128 255;
  --colors-primary-600: 63 120 255;
  --colors-primary-700: 55 109 255;
  --colors-primary-800: 47 99 255;
  --colors-primary-900: 32 80 255;
  --colors-primary-950: 27 67 213;
  --colors-primary: 70 128 255;
}
.preset-2{
  --colors-primary-50: 254 242 242;
  --colors-primary-100: 254 226 226;
  --colors-primary-200: 254 202 202;
  --colors-primary-300: 252 165 165;
  --colors-primary-400: 248 113 113;
  --colors-primary-500: 239 68 68;
  --colors-primary-600: 220 38 38;
  --colors-primary-700: 185 28 28;
  --colors-primary-800: 153 27 27;
  --colors-primary-900: 127 29 29;
  --colors-primary-950: 69 10 10;
  --colors-primary: 239 68 68;
}
.preset-3{
  --colors-primary-50: 255 247 237;
  --colors-primary-100: 255 237 213;
  --colors-primary-200: 254 215 170;
  --colors-primary-300: 253 186 116;
  --colors-primary-400: 251 146 60;
  --colors-primary-500: 249 115 22;
  --colors-primary-600: 234 88 12;
  --colors-primary-700: 194 65 12;
  --colors-primary-800: 154 52 18;
  --colors-primary-900: 124 45 18;
  --colors-primary-950: 67 20 7;
  --colors-primary: 249 115 22;
}
.preset-4{
  --colors-primary-50: 255 251 235;
  --colors-primary-100: 254 243 199;
  --colors-primary-200: 253 230 138;
  --colors-primary-300: 252 211 77;
  --colors-primary-400: 251 191 36;
  --colors-primary-500: 245 158 11;
  --colors-primary-600: 217 119 6;
  --colors-primary-700: 180 83 9;
  --colors-primary-800: 146 64 14;
  --colors-primary-900: 120 53 15;
  --colors-primary-950: 69 26 3;
  --colors-primary: 245 158 11;
}
.preset-5{
  --colors-primary-50: 254 252 232;
  --colors-primary-100: 254 249 195;
  --colors-primary-200: 254 240 138;
  --colors-primary-300: 253 224 71;
  --colors-primary-400: 250 204 21;
  --colors-primary-500: 234 179 8;
  --colors-primary-600: 202 138 4;
  --colors-primary-700: 161 98 7;
  --colors-primary-800: 133 77 14;
  --colors-primary-900: 113 63 18;
  --colors-primary-950: 66 32 6;
  --colors-primary: 234 179 8;
}
.preset-6{
  --colors-primary-50: 247 254 231;
  --colors-primary-100: 236 252 203;
  --colors-primary-200: 217 249 157;
  --colors-primary-300: 190 242 100;
  --colors-primary-400: 163 230 53;
  --colors-primary-500: 132 204 22;
  --colors-primary-600: 101 163 13;
  --colors-primary-700: 77 124 15;
  --colors-primary-800: 63 98 18;
  --colors-primary-900: 54 83 20;
  --colors-primary-950: 26 46 5;
  --colors-primary: 132 204 22;
}
.preset-7{
  --colors-primary-50: 240 253 244;
  --colors-primary-100: 220 252 231;
  --colors-primary-200: 187 247 208;
  --colors-primary-300: 134 239 172;
  --colors-primary-400: 74 222 128;
  --colors-primary-500: 34 197 94;
  --colors-primary-600: 22 163 74;
  --colors-primary-700: 21 128 61;
  --colors-primary-800: 22 101 52;
  --colors-primary-900: 20 83 45;
  --colors-primary-950: 5 46 22;
  --colors-primary: 34 197 94;
}
.preset-8{
  --colors-primary-50: 236 253 245;
  --colors-primary-100: 209 250 229;
  --colors-primary-200: 167 243 208;
  --colors-primary-300: 110 231 183;
  --colors-primary-400: 52 211 153;
  --colors-primary-500: 16 185 129;
  --colors-primary-600: 5 150 105;
  --colors-primary-700: 4 120 87;
  --colors-primary-800: 6 95 70;
  --colors-primary-900: 6 78 59;
  --colors-primary-950: 2 44 34;
  --colors-primary: 16 185 129;
}
.preset-9{
  --colors-primary-50: 240 253 250;
  --colors-primary-100: 204 251 241;
  --colors-primary-200: 153 246 228;
  --colors-primary-300: 94 234 212;
  --colors-primary-400: 45 212 191;
  --colors-primary-500: 20 184 166;
  --colors-primary-600: 13 148 136;
  --colors-primary-700: 15 118 110;
  --colors-primary-800: 17 94 89;
  --colors-primary-900: 19 78 74;
  --colors-primary-950: 4 47 46;
  --colors-primary: 20 184 166;
}
.preset-10{
  --colors-primary-50: 236 254 255;
  --colors-primary-100: 207 250 254;
  --colors-primary-200: 165 243 252;
  --colors-primary-300: 103 232 249;
  --colors-primary-400: 34 211 238;
  --colors-primary-500: 6 182 212;
  --colors-primary-600: 8 145 178;
  --colors-primary-700: 14 116 144;
  --colors-primary-800: 21 94 117;
  --colors-primary-900: 22 78 99;
  --colors-primary-950: 8 51 68;
  --colors-primary: 6 182 212;
}
.preset-11{
  --colors-primary-50: 240 249 255;
  --colors-primary-100: 224 242 254;
  --colors-primary-200: 186 230 253;
  --colors-primary-300: 125 211 252;
  --colors-primary-400: 56 189 248;
  --colors-primary-500: 14 165 233;
  --colors-primary-600: 2 132 199;
  --colors-primary-700: 3 105 161;
  --colors-primary-800: 7 89 133;
  --colors-primary-900: 12 74 110;
  --colors-primary-950: 8 47 73;
  --colors-primary: 14 165 233;
}
.preset-12{
  --colors-primary-50: 239 246 255;
  --colors-primary-100: 219 234 254;
  --colors-primary-200: 191 219 254;
  --colors-primary-300: 147 197 253;
  --colors-primary-400: 96 165 250;
  --colors-primary-500: 59 130 246;
  --colors-primary-600: 37 99 235;
  --colors-primary-700: 29 78 216;
  --colors-primary-800: 30 64 175;
  --colors-primary-900: 30 58 138;
  --colors-primary-950: 23 37 84;
  --colors-primary: 59 130 246;
}
.preset-13{
  --colors-primary-50: 238 242 255;
  --colors-primary-100: 224 231 255;
  --colors-primary-200: 199 210 254;
  --colors-primary-300: 165 180 252;
  --colors-primary-400: 129 140 248;
  --colors-primary-500: 99 102 241;
  --colors-primary-600: 79 70 229;
  --colors-primary-700: 67 56 202;
  --colors-primary-800: 55 48 163;
  --colors-primary-900: 49 46 129;
  --colors-primary-950: 30 27 75;
  --colors-primary: 99 102 241;
}
.preset-14{
  --colors-primary-50: 245 243 255;
  --colors-primary-100: 237 233 254;
  --colors-primary-200: 221 214 254;
  --colors-primary-300: 196 181 253;
  --colors-primary-400: 167 139 250;
  --colors-primary-500: 139 92 246;
  --colors-primary-600: 124 58 237;
  --colors-primary-700: 109 40 217;
  --colors-primary-800: 91 33 182;
  --colors-primary-900: 76 29 149;
  --colors-primary-950: 46 16 101;
  --colors-primary: 139 92 246;
}
.preset-15{
  --colors-primary-50: 250 245 255;
  --colors-primary-100: 243 232 255;
  --colors-primary-200: 233 213 255;
  --colors-primary-300: 216 180 254;
  --colors-primary-400: 192 132 252;
  --colors-primary-500: 168 85 247;
  --colors-primary-600: 147 51 234;
  --colors-primary-700: 126 34 206;
  --colors-primary-800: 107 33 168;
  --colors-primary-900: 88 28 135;
  --colors-primary-950: 59 7 100;
  --colors-primary: 168 85 247;
}
.preset-16{
  --colors-primary-50: 253 244 255;
  --colors-primary-100: 250 232 255;
  --colors-primary-200: 245 208 254;
  --colors-primary-300: 240 171 252;
  --colors-primary-400: 232 121 249;
  --colors-primary-500: 217 70 239;
  --colors-primary-600: 192 38 211;
  --colors-primary-700: 162 28 175;
  --colors-primary-800: 134 25 143;
  --colors-primary-900: 112 26 117;
  --colors-primary-950: 74 4 78;
  --colors-primary: 217 70 239;
}
.preset-17{
  --colors-primary-50: 253 242 248;
  --colors-primary-100: 252 231 243;
  --colors-primary-200: 251 207 232;
  --colors-primary-300: 249 168 212;
  --colors-primary-400: 244 114 182;
  --colors-primary-500: 236 72 153;
  --colors-primary-600: 219 39 119;
  --colors-primary-700: 190 24 93;
  --colors-primary-800: 157 23 77;
  --colors-primary-900: 131 24 67;
  --colors-primary-950: 80 7 36;
  --colors-primary: 236 72 153;
}
.preset-18{
  --colors-primary-50: 255 241 242;
  --colors-primary-100: 255 228 230;
  --colors-primary-200: 254 205 211;
  --colors-primary-300: 253 164 175;
  --colors-primary-400: 251 113 133;
  --colors-primary-500: 244 63 94;
  --colors-primary-600: 225 29 72;
  --colors-primary-700: 190 18 60;
  --colors-primary-800: 159 18 57;
  --colors-primary-900: 136 19 55;
  --colors-primary-950: 76 5 25;
  --colors-primary: 244 63 94;
}
h1,.h1{
  font-size: 38px;
  font-weight: 700;
}
h2,.h2{
  font-size: 30px;
  font-weight: 700;
}
h3,.h3{
  font-size: 24px;
  font-weight: 600;
}
h4,.h4{
  font-size: 20px;
  font-weight: 600;
}
h5,.h5{
  font-size: 16px;
  font-weight: 600;
}
h6,.h6{
  font-size: 14px;
  font-weight: 600;
}
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6,{
  line-height: 1.2;
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
h1:is([data-pc-theme="dark"] *), h2:is([data-pc-theme="dark"] *), h3:is([data-pc-theme="dark"] *), h4:is([data-pc-theme="dark"] *), h5:is([data-pc-theme="dark"] *), h6:is([data-pc-theme="dark"] *), .h1:is([data-pc-theme="dark"] *), .h2:is([data-pc-theme="dark"] *), .h3:is([data-pc-theme="dark"] *), .h4:is([data-pc-theme="dark"] *), .h5:is([data-pc-theme="dark"] *), .h6:is([data-pc-theme="dark"] *),{
  color: rgba(255, 255, 255, 0.8);
}
body{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
  font-size: 0.875rem;
  line-height: 1.5;
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
body:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
b, strong{
  font-weight: 600;
}
.text-muted{
  color: rgba(33, 37, 41, 0.75);
}
.text-muted:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(116 136 146 / var(--tw-text-opacity));
}
.material-icons-two-tone{
  -webkit-background-clip: text;
          background-clip: text;
  -webkit-text-fill-color: transparent;
}
.material-icons-two-tone:not([class*="bg-"]){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.material-icons-two-tone:not([class*="bg-"]):is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(191 191 191 / var(--tw-bg-opacity));
}
*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
.container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}
@media (min-width: 640px){
  .container{
    max-width: 640px;
  }
}
@media (min-width: 768px){
  .container{
    max-width: 768px;
  }
}
@media (min-width: 1024px){
  .container{
    max-width: 1024px;
  }
}
@media (min-width: 1280px){
  .container{
    max-width: 1280px;
  }
}
@media (min-width: 1536px){
  .container{
    max-width: 1536px;
  }
}
.pc-header{
  position: fixed;
  z-index: 1025;
  display: flex;
  height: 74px;
  background-color: rgba( 248,249,250, 0.7);
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
  --tw-backdrop-blur: blur(7px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.pc-header:is([data-pc-theme="dark"] *){
  background-color: rgba( 19, 25, 32, 0.5);
  color: rgba(255, 255, 255, 0.8);
}
.pc-header:where([dir="ltr"], [dir="ltr"] *){
  right: 0px;
}
@media not all and (min-width: 1024px){
  .pc-header:where([dir="ltr"], [dir="ltr"] *){
    left: 0px;
  }
}
@media (min-width: 1024px){
  .pc-header:where([dir="ltr"], [dir="ltr"] *){
    left: 280px;
  }
}
.pc-header:where([dir="rtl"], [dir="rtl"] *){
  left: 0px;
}
@media not all and (min-width: 1024px){
  .pc-header:where([dir="rtl"], [dir="rtl"] *){
    right: 0px;
  }
}
@media (min-width: 1024px){
  .pc-header:where([dir="rtl"], [dir="rtl"] *){
    right: 280px;
  }
}
.pc-header .pc-head-link{
  position: relative;
  margin-left: 0.25rem;
  margin-right: 0.25rem;
  display: flex;
  height: 2.75rem;
  width: 2.75rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.pc-header .pc-head-link::after{
  position: absolute;
  inset: 0px;
  z-index: 1;
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(243 245 247 / var(--tw-bg-opacity));
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-content: "" !important;
  content: var(--tw-content) !important;
}
.pc-header .pc-head-link:hover::after{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  content: var(--tw-content);
  border-radius: 0.25rem;
}
.pc-header .pc-head-link:is([data-pc-theme="dark"] *)::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(25 33 42 / var(--tw-bg-opacity));
}
.pc-header .pc-head-link i, .pc-header .pc-head-link svg, .pc-header .pc-head-link img{
  position: relative;
  z-index: 5;
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.pc-header .pc-head-link i{
  font-size: 1.5rem;
  line-height: 2rem;
  line-height: 1;
}
.pc-header .pc-head-link svg{
  height: 1.5rem;
  width: 1.5rem;
}
.pc-header .pc-head-link:hover i, .pc-header .pc-head-link:hover svg{
  --tw-scale-x: 1.08;
  --tw-scale-y: 1.08;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@media not all and (min-width: 640px){
  .pc-header .pc-h-item.dropdown{
    position: static;
  }
}
.pc-header .pc-h-item.dropdown .dropdown-menu{
  max-width: 100%;
}
@media not all and (min-width: 640px){
  .pc-header .pc-h-item.dropdown .dropdown-menu{
    left: 15px !important;
    right: 15px !important;
    top: 100% !important;
    min-width: calc(100vw - 30px);
  }
}
.pc-header .pc-h-item.dropdown .dropdown-menu:not(.dropdown-menu-end):where([dir="rtl"], [dir="rtl"] *){
  right: 0px !important;
  left: auto !important;
}
@media not all and (min-width: 640px){
  .pc-header .pc-h-item.dropdown.drp-show .dropdown-menu{
    transform: none !important;
  }
}
@media (min-width: 640px){
  .pc-header .dropdown-menu.dropdown-notification{
    min-width: 450px;
  }
}
.pc-header .dropdown-menu.dropdown-notification .card{
  cursor: pointer;
}
.pc-header .dropdown-menu.dropdown-notification .card:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 245 247 / var(--tw-bg-opacity));
}
.pc-header .dropdown-menu.dropdown-notification .card:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(25 33 42 / var(--tw-bg-opacity));
}
@media (min-width: 640px){
  .pc-header .dropdown-menu.dropdown-user-profile{
    min-width: 352px;
  }
}
.pc-header .dropdown-menu.dropdown-user-profile .dropdown-item{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 0.5rem;
}
.offcanvas.offcanvas-end.pc-announcement-offcanvas{
  right: -474px;
  z-index: 1028;
  width: 474px;
}
.offcanvas.offcanvas-end.pc-announcement-offcanvas .offcanvas-body{
  height: calc(100vh - 68px);
}
.pc-sidebar{
  position: fixed;
  top: 0px;
  bottom: 0px;
  z-index: 1026;
  width: 280px;
  overflow: hidden;
  border-style: dashed;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.pc-sidebar:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(36 45 57 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
@media not all and (min-width: 1024px){
  .pc-sidebar{
    left: -280px;
  }
}
.pc-sidebar:where([dir="ltr"], [dir="ltr"] *){
  border-right-width: 1px;
}
.pc-sidebar:where([dir="rtl"], [dir="rtl"] *){
  border-left-width: 1px;
}
.pc-sidebar .navbar-wrapper{
  width: 280px;
  background-color: inherit;
}
.pc-sidebar .navbar-content{
  position: relative;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0px;
  padding-right: 0px;
  height: calc(100vh - 74px);
}
@media (min-width: 1024px){
  .pc-sidebar.pc-sidebar-hide{
    width: 0px;
  }
}
.pc-sidebar.pc-sidebar-hide:where([dir="ltr"], [dir="ltr"] *){
  border-right-width: 0px;
}
.pc-sidebar.pc-sidebar-hide:where([dir="rtl"], [dir="rtl"] *){
  border-left-width: 0px;
}
@media not all and (min-width: 1024px){
  .pc-sidebar.mob-sidebar-active{
    left: 0px;
  }
}
.pc-sidebar.mob-sidebar-active .navbar-wrapper{
  position: relative;
  z-index: 5;
  background-color: inherit;
}
.pc-sidebar .pc-menu-overlay{
  position: fixed;
  inset: 0px;
  background-color: rgba(0,0,0,.15);
  --tw-backdrop-blur: blur(3px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.pc-navbar > *{
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}
.pc-navbar .pc-caption{
  display: block;
  padding-left: 23px;
  padding-right: 23px;
  padding-top: 1.5rem;
  padding-bottom: 0.5rem;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}
.pc-navbar .pc-caption:first-child{
  padding-top: 0.625rem;
}
.pc-navbar .pc-caption svg{
  display: none;
}
.pc-navbar .pc-link{
  position: relative;
  display: block;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
  font-size: 0.875rem;
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.pc-navbar .pc-link:is([data-pc-theme="dark"] *){
  color: rgba(255, 255, 255, 0.5);
}
.pc-navbar .pc-link .pc-micon{
  display: inline-block;
  height: 1.5rem;
  width: 1.5rem;
  text-align: center;
  vertical-align: middle;
}
.pc-navbar .pc-link .pc-micon:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 15px;
}
.pc-navbar .pc-link .pc-micon:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 15px;
}
.pc-navbar .pc-link .pc-micon > svg{
  display: inline-block;
  height: 22px;
  width: 22px;
}
.pc-navbar .pc-link .pc-arrow{
  position: relative;
  display: inline-block;
  line-height: 1.375;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.pc-navbar .pc-link .pc-arrow::after{
  position: absolute;
  inset: 0px;
  --tw-content: "";
  content: var(--tw-content);
}
.pc-navbar .pc-link .pc-arrow:where([dir="ltr"], [dir="ltr"] *){
  float: right;
}
.pc-navbar .pc-link .pc-arrow:where([dir="rtl"], [dir="rtl"] *){
  float: left;
}
.pc-navbar .pc-link .pc-arrow > svg{
  display: inline-block;
  height: 0.875rem;
  width: 0.875rem;
}
.pc-navbar .pc-link .pc-badge{
  display: inline-flex;
  height: 1.25rem;
  width: 1.25rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: rgb(var(--colors-primary-500));
  font-size: 10px;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.pc-navbar .pc-link .pc-badge:where([dir="ltr"], [dir="ltr"] *){
  float: right;
  margin-right: 5px;
}
.pc-navbar .pc-link .pc-badge:where([dir="rtl"], [dir="rtl"] *){
  float: left;
  margin-left: 5px;
}
.pc-navbar >.pc-item >.pc-link::after{
  position: absolute;
  inset: 2px;
  border-radius: 8px;
  opacity: 0.1;
  --tw-content: "";
  content: var(--tw-content);
}
.pc-navbar >.pc-item >.pc-link:hover::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.pc-navbar >.pc-item.active >.pc-link{
  color: rgb(var(--colors-primary-500));
}
.pc-navbar >.pc-item.active >.pc-link::after{
  content: var(--tw-content);
  background-color: rgb(var(--colors-primary-500));
}
.pc-navbar >.pc-item .pc-submenu .pc-item.active >.pc-link, .pc-navbar >.pc-item .pc-submenu .pc-item.pc-trigger >.pc-link{
  font-weight: 500;
}
.pc-navbar >.pc-item .pc-submenu .pc-item.active >.pc-link::after, .pc-navbar >.pc-item .pc-submenu .pc-item.pc-trigger >.pc-link::after{
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-color: rgb(var(--colors-primary-500));
  content: var(--tw-content);
  opacity: 1;
}
.pc-navbar >.pc-item .pc-submenu .pc-item.active >.pc-link:hover::after, .pc-navbar >.pc-item .pc-submenu .pc-item.pc-trigger >.pc-link:hover::after{
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  content: var(--tw-content);
  background-color: rgb(var(--colors-primary-500));
}
.pc-navbar >.pc-item .pc-submenu .pc-link{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-right: 30px;
}
.pc-navbar >.pc-item .pc-submenu .pc-link::after{
  position: absolute;
  top: 1.25rem;
  height: 5px;
  width: 5px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  opacity: 0.5;
  --tw-content: "";
  content: var(--tw-content);
}
.pc-navbar >.pc-item .pc-submenu .pc-link:hover::after{
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-color: rgb(var(--colors-primary-500));
  content: var(--tw-content);
  opacity: 1;
}
.pc-navbar >.pc-item .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 60px;
}
.pc-navbar >.pc-item .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 1.75rem;
}
.pc-navbar >.pc-item .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 60px;
}
.pc-navbar >.pc-item .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 1.75rem;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 5rem;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 62px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 5rem;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 62px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 95px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 79px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 95px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 79px;
}
[data-pc-sidebar-caption="false"] .pc-sidebar .pc-caption{
  display: none;
}
.pc-container{
  position: relative;
  top: 74px;
  min-height: calc(100vh - 135px);
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
@media not all and (min-width: 1024px){
  .pc-container{
    margin-left: 0px;
  }
}
@media (min-width: 1024px){
  .pc-container:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 280px;
  }
  .pc-container:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 280px;
  }
}
.pc-container .pc-content{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
  padding-top: 1.25rem;
}
@media not all and (min-width: 640px){
  .pc-container .pc-content{
    padding: 15px;
  }
}
.page-header{
  margin-bottom: 1.5rem;
  padding-top: 13px;
  padding-bottom: 13px;
}
@media not all and (min-width: 640px){
  .page-header{
    padding-left: 0.625rem;
    padding-right: 0.625rem;
  }
}
.page-header .breadcrumb{
  font-size: 13px;
}
.pc-footer{
  position: relative;
  z-index: 995;
  margin-top: 74px;
  padding-top: 15px;
  padding-bottom: 15px;
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
@media not all and (min-width: 1024px){
  .pc-footer{
    margin-left: 0px;
  }
}
@media (min-width: 1024px){
  .pc-footer:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 280px;
  }
  .pc-footer:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 280px;
  }
  .pc-sidebar.pc-sidebar-hide ~.pc-header:where([dir="ltr"], [dir="ltr"] *){
    left: 0px;
  }
  .pc-sidebar.pc-sidebar-hide ~.pc-header:where([dir="rtl"], [dir="rtl"] *){
    right: 0px;
  }
  .pc-sidebar.pc-sidebar-hide ~.pc-container:where([dir="ltr"], [dir="ltr"] *), .pc-sidebar.pc-sidebar-hide ~.pc-footer:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 0px;
  }
  .pc-sidebar.pc-sidebar-hide ~.pc-container:where([dir="rtl"], [dir="rtl"] *), .pc-sidebar.pc-sidebar-hide ~.pc-footer:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 0px;
  }
}
.footer-wrapper.container, .pc-content.container{
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 768px){
  .footer-wrapper.container, .pc-content.container{
    max-width: 540px;
  }
}
@media (min-width: 1024px){
  .footer-wrapper.container, .pc-content.container{
    max-width: 960px;
  }
}
@media (min-width: 1536px){
  .footer-wrapper.container, .pc-content.container{
    max-width: 1140px;
  }
}
[data-pc-theme_contrast="true"] body{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
[data-pc-theme_contrast="true"] body:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
[data-pc-theme_contrast="true"] .pc-sidebar{
  border-right-width: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 1px 0 3px 0 rgba(219,224,229,1);
  --tw-shadow-colored: 1px 0 3px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[data-pc-theme_contrast="true"] .pc-sidebar:is([data-pc-theme="dark"] *){
  border-right-width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[data-pc-theme_contrast="true"] .card{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.08);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
:not(pre) > code[class*=language-], pre[class*=language-]{
  margin: 0px;
  margin-top: 1rem;
  display: flex;
}
:not(pre) > code[class*=language-] > code, pre[class*=language-] > code{
  width: 100%;
}
.apexcharts-legend-text:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
text:is([data-pc-theme="dark"] *),.apexcharts-theme-light .apexcharts-menu-icon:hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-reset-icon:hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-selection-icon:not(.apexcharts-selected):hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-zoom-icon:not(.apexcharts-selected):hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-zoomin-icon:hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-zoomout-icon:hover svg:is([data-pc-theme="dark"] *){
  fill: #bfbfbf !important;
}
.apexcharts-gridline:is([data-pc-theme="dark"] *),.apexcharts-xaxis-tick:is([data-pc-theme="dark"] *),.apexcharts-grid-borders:is([data-pc-theme="dark"] *){
  display: none;
}
.apexcharts-canvas{
  direction: ltr;
}
.apexcharts-tooltip.apexcharts-theme-light:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.apexcharts-xaxistooltip:is([data-pc-theme="dark"] *), .apexcharts-yaxistooltip:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.apexcharts-xaxistooltip-bottom:is([data-pc-theme="dark"] *):after, .apexcharts-xaxistooltip-bottom:is([data-pc-theme="dark"] *):before{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.apexcharts-menu:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.apexcharts-theme-light .apexcharts-menu-item:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.jvm-element:is([data-pc-theme="dark"] *){
  fill: #263240 !important;
}
.vtree li.vtree-leaf.vtree-selected > a.vtree-leaf-label:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1 !important;
  background-color: rgb(38 50 64 / var(--tw-bg-opacity)) !important;
  outline-color: #303f50 !important;
}
@media (min-width: 1025px){
  [data-pc-layout="horizontal"] .pc-header{
    z-index: 1027;
    background-color: rgba( 248,249,250, 0.7);
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    --tw-backdrop-blur: blur(7px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  }
  [data-pc-layout="horizontal"] .pc-header:is([data-pc-theme="dark"] *){
    background-color: rgba( 19, 25, 32, 0.5);
  }
  [data-pc-layout="horizontal"] .pc-header .pc-h-item.pc-sidebar-collapse{
    display: none;
  }
  [data-pc-layout="horizontal"] .pc-sidebar{
    height: 60px;
    width: 100%;
    overflow: visible;
    background-color: rgba( 248,249,250, 0.7);
  }
  [data-pc-layout="horizontal"] .pc-sidebar:is([data-pc-theme="dark"] *){
    background-color: rgba( 19, 25, 32, 0.5);
  }
  [data-pc-layout="horizontal"] .pc-sidebar .navbar-content,[data-pc-layout="horizontal"] .pc-sidebar .m-header{
    background-color: rgba( 248,249,250, 0.7);
    --tw-backdrop-blur: blur(7px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  }
  [data-pc-layout="horizontal"] .pc-sidebar .navbar-content:is([data-pc-theme="dark"] *),[data-pc-layout="horizontal"] .pc-sidebar .m-header:is([data-pc-theme="dark"] *){
    background-color: rgba( 19, 25, 32, 0.5);
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-badge,[data-pc-layout="horizontal"] .pc-sidebar .pc-caption:not(:first-child):after{
    display: none;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .navbar-wrapper{
    width: 100%;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .navbar-content{
    height: 57px;
    border-bottom-width: 1px;
    border-style: dashed;
    --tw-border-opacity: 1;
    border-color: rgb(190 200 208 / var(--tw-border-opacity));
    background-color: rgba( 248,249,250, 0.7);
    padding: 6px 20px;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .navbar-content:is([data-pc-theme="dark"] *){
    --tw-border-opacity: 1;
    border-color: rgb(36 45 57 / var(--tw-border-opacity));
    background-color: rgba( 19, 25, 32, 0.5);
  }
  [data-pc-layout="horizontal"] .pc-sidebar .navbar-content .simplebar-mask, [data-pc-layout="horizontal"] .pc-sidebar .navbar-content .simplebar-content-wrapper{
    overflow: visible !important;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-link{
    display: block;
    align-items: center;
    padding: 10px 14px;
    font-size: 0.875rem;
    font-weight: 500;
    --tw-text-opacity: 1;
    color: rgb(91 107 121 / var(--tw-text-opacity));
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-link:is([data-pc-theme="dark"] *){
    color: rgba(255, 255, 255, 0.5);
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-link:active,[data-pc-layout="horizontal"] .pc-sidebar .pc-link:focus,[data-pc-layout="horizontal"] .pc-sidebar .pc-link:hover{
    color: rgb(var(--colors-primary-500));
    -webkit-text-decoration-line: none;
            text-decoration-line: none;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-link .pc-micon i{
    vertical-align: middle;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .card{
    display: none;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar{
    display: inline-block !important;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar .pc-link{
    display: flex;
    align-items: center;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar > .pc-item{
    position: relative;
    margin: 0px;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar > .pc-item:hover:not(.active) > .pc-link{
    color: rgb(var(--colors-primary-500));
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar > .pc-item:hover:not(.active) > .pc-link::after{
    content: var(--tw-content);
    background-color: rgb(var(--colors-primary-500));
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar > .pc-item .pc-submenu .pc-link{
    padding: 12px 30px !important;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar > .pc-item .pc-submenu .pc-submenu .pc-link{
    padding: 12px 30px !important;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar > .pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link{
    padding: 12px 30px !important;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar > .pc-item > .pc-submenu.edge{
    left: auto;
    right: 0px;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar > .pc-item > .pc-link{
    margin-left: 0.125rem;
    margin-right: 0.125rem;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar > .pc-item > .pc-link > .pc-arrow{
    margin-left: 0.625rem;
    --tw-rotate: 90deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar .pc-item{
    display: inline-block;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-navbar .pc-item.pc-caption{
    padding: 0px !important;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-arrow{
    display: inline-block;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-arrow:where([dir="ltr"], [dir="ltr"] *){
    float: right;
    margin-left: auto;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-arrow:where([dir="rtl"], [dir="rtl"] *){
    float: left;
    margin-right: auto;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-arrow > svg{
    height: 14px;
    width: 14px;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu{
    position: absolute;
    min-width: 225px;
    border-radius: 0.25rem;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity));
    padding-top: 15px;
    padding-bottom: 15px;
    --tw-shadow: 0 4px 24px 0 rgba(62,57,107,0.18);
    --tw-shadow-colored: 0 4px 24px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu::before{
    content: var(--tw-content);
    display: none;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu:is([data-pc-theme="dark"] *){
    --tw-bg-opacity: 1;
    background-color: rgb(38 50 64 / var(--tw-bg-opacity));
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item{
    position: relative;
    display: block;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item .pc-submenu > .pc-item:before,[data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item::before{
    left: 1.25rem;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item .pc-link{
    position: relative;
    padding: 12px 15px 12px 20px;
    --tw-text-opacity: 1;
    color: rgb(91 107 121 / var(--tw-text-opacity));
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item .pc-link::after{
    content: var(--tw-content);
    display: none;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item .pc-link:is([data-pc-theme="dark"] *){
    --tw-text-opacity: 1;
    color: rgb(191 191 191 / var(--tw-text-opacity));
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item .pc-link .pc-icon svg, [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item .pc-link .pc-icon i{
    --tw-text-opacity: 1;
    color: rgb(91 107 121 / var(--tw-text-opacity));
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item .pc-link .pc-icon svg:is([data-pc-theme="dark"] *), [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item .pc-link .pc-icon i:is([data-pc-theme="dark"] *){
    color: rgba(255, 255, 255, 0.5);
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item:hover > .pc-link, [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-item > .pc-link:hover{
    color: rgb(var(--colors-primary-500));
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu{
    left: 100%;
    top: -15px;
    z-index: 1;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu.edge{
    left: auto;
    right: 100%;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu.edge-alt{
    top: auto;
    bottom: -15px;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu.edge-alt.edge-alt-full::-webkit-scrollbar{
    width: 0.375rem;
    opacity: 0;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu.edge-alt.edge-alt-full::-webkit-scrollbar:hover{
    opacity: 1;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu.edge-alt.edge-alt-full::-webkit-scrollbar-track{
    background-color: transparent;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu.edge-alt.edge-alt-full::-webkit-scrollbar-thumb{
    background-color: rgb(91 107 121 / 0.1);
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu.edge-alt.edge-alt-full::-webkit-scrollbar-thumb:hover{
    background-color: rgb(91 107 121 / 0.3);
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu.edge-alt.edge-alt-full .pc-submenu{
    left: 200px;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-submenu .pc-submenu.edge-alt.edge-alt-full .pc-submenu.edge{
    left: 0px;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-item .pc-submenu{
    display: none;
  }
  [data-pc-layout="horizontal"] .pc-sidebar .pc-item:hover >.pc-submenu{
    display: block !important;
  }
  [data-pc-layout="horizontal"] .pc-container:where([dir="ltr"], [dir="ltr"] *),[data-pc-layout="horizontal"] .pc-footer:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 0px;
  }
  [data-pc-layout="horizontal"] .pc-container:where([dir="rtl"], [dir="rtl"] *),[data-pc-layout="horizontal"] .pc-footer:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 0px;
  }
  [data-pc-layout="horizontal"] .pc-container{
    top: calc(74px + 60px);
    min-height: calc(100vh - 74px - 60px * 2);
  }
  [data-pc-layout="horizontal"] .pc-footer{
    top: 60px;
  }
}
[data-pc-layout="color-header"] body{
  position: relative;
}
[data-pc-layout="color-header"] body:not([class*="bg-"]){
  background-color: rgb(var(--colors-primary-500));
}
[data-pc-layout="color-header"] .pc-header{
  position: absolute;
  background-color: transparent;
}
[data-pc-layout="color-header"] .pc-header .pc-head-link{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
[data-pc-layout="color-header"] .pc-header .pc-head-link::after{
  content: var(--tw-content);
  background-color: rgba(255,255,255,0.15);
}
[data-pc-layout="color-header"] .pc-header .pc-head-link svg, [data-pc-layout="color-header"] .pc-header .pc-head-link i{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
[data-pc-layout="color-header"] .pc-sidebar{
  position: absolute;
  height: 100%;
  border-width: 0px;
  background-color: transparent;
}
[data-pc-layout="color-header"] .pc-sidebar .navbar-wrapper{
  height: 100%;
}
[data-pc-layout="color-header"] .pc-sidebar .navbar-content{
  position: relative;
  border-radius: 0 12px 0 0;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 18px 0 10px;
  --tw-shadow: inset 0 0 1px 1px #fff;
  --tw-shadow-colored: inset 0 0 1px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: #e7eaee;
  --tw-shadow: var(--tw-shadow-colored);
}
[data-pc-layout="color-header"] .pc-sidebar .navbar-content:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
  --tw-shadow-color: #303f50;
  --tw-shadow: var(--tw-shadow-colored);
}
[data-pc-layout="color-header"] .pc-sidebar .navbar-content{
  min-height: calc(100% - 74px);
}
[data-pc-layout="color-header"] .pc-footer{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
}
[data-pc-layout="color-header"] .pc-footer:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
[data-pc-layout="color-header"] .pc-container{
  padding-top: 140px;
}
[data-pc-layout="color-header"] .pc-container .pc-content{
  min-height: calc(100vh - 273px);
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
}
[data-pc-layout="color-header"] .pc-container .pc-content:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
[data-pc-layout="color-header"] .pc-container .page-header{
  margin-top: -140px;
  padding: 0px;
}
[data-pc-layout="color-header"] .pc-container .page-header h2{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
[data-pc-layout="color-header"] .pc-container .page-header .breadcrumb{
  margin-bottom: 5px;
}
[data-pc-layout="color-header"] .pc-container .page-header .breadcrumb .breadcrumb-item, [data-pc-layout="color-header"] .pc-container .page-header .breadcrumb a{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
[data-pc-layout="compact"] .pc-sidebar .pc-user-card{
  display: none;
}
[data-pc-layout="compact"] .pc-sidebar .pc-micon{
  height: 27px;
  width: 46px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-micon svg{
  height: 26px;
  width: 26px;
}
[data-pc-layout="compact"] .pc-sidebar .m-header .logo.logo-sm{
  width: 50px;
}
[data-pc-layout="compact"] .pc-sidebar .m-header .badge{
  display: none;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item{
  margin: 4px 14px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item.pc-hasmenu > .pc-submenu{
  position: absolute;
  left: 94px;
  top: 0px;
  height: 100vh;
  min-width: 186px;
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item.pc-hasmenu > .pc-submenu:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item > .pc-hasmenu .pc-link{
  padding: 12px 30px 12px 45px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item > .pc-hasmenu .pc-link::after{
  content: var(--tw-content);
  left: 30px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item .pc-hasmenu .pc-hasmenu .pc-link{
  padding: 12px 30px 12px 52px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item .pc-hasmenu .pc-hasmenu .pc-link::after{
  content: var(--tw-content);
  left: 40px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item .pc-hasmenu .pc-hasmenu .pc-hasmenu .pc-link{
  padding: 12px 30px 12px 70px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item .pc-hasmenu .pc-hasmenu .pc-hasmenu .pc-link::after{
  content: var(--tw-content);
  left: 52px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item > .pc-link{
  display: inline-block;
  padding: 14px 12px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item > .pc-link .pc-micon{
  margin-right: 0px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-navbar > .pc-item > .pc-link .pc-micon i{
  vertical-align: middle;
  font-size: 22px;
}
[data-pc-layout="compact"] .pc-sidebar:not(.pc-compact-submenu-active){
  width: 100px;
}
[data-pc-layout="compact"] .pc-sidebar:not(.pc-compact-submenu-active) .m-header{
  width: 100px;
  padding: 16px 12px;
}
[data-pc-layout="compact"] .pc-sidebar:not(.pc-compact-submenu-active) .m-header > a{
  margin-top: 0px;
  margin-bottom: 0px;
  margin-left: auto;
  margin-right: auto;
}
[data-pc-layout="compact"] .pc-sidebar:not(.pc-compact-submenu-active) .m-header .logo.logo-lg{
  display: none;
}
[data-pc-layout="compact"] .pc-sidebar:not(.pc-compact-submenu-active) .m-header .logo.logo-sm{
  display: inline-block;
}
@media (min-width: 1025px){
  [data-pc-layout="compact"] .pc-sidebar:not(.pc-compact-submenu-active) ~ .pc-footer,[data-pc-layout="compact"] .pc-sidebar:not(.pc-compact-submenu-active) ~ .pc-container{
    margin-left: 100px;
  }
}
@media (max-width: 1024.98px){
  [data-pc-layout="compact"] .pc-sidebar:not(.pc-compact-submenu-active) .m-header{
    width: 100px;
  }
  [data-pc-layout="compact"] .pc-sidebar:not(.pc-compact-submenu-active) .m-header .b-brand{
    margin-top: 0px;
    margin-bottom: 0px;
    margin-left: auto;
    margin-right: auto;
    width: 50px;
    overflow: hidden;
  }
  [data-pc-layout="compact"] .pc-sidebar:not(.mob-sidebar-active){
    left: -300px;
  }
}
[data-pc-layout="compact"] .pc-sidebar .navbar-content{
  width: 100px;
  height: calc(100vh - 74px);
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu{
  position: relative;
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu::after{
  position: absolute;
  left: 0px;
  top: 0px;
  height: calc(100% - 30px);
  width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(231 234 238 / var(--tw-bg-opacity));
  --tw-content: "";
  content: var(--tw-content);
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu:is([data-pc-theme="dark"] *)::after{
  --tw-bg-opacity: 1;
  background-color: rgb(48 63 80 / var(--tw-bg-opacity));
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-title{
  margin-bottom: 14px;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-bottom-color: rgb(231 234 238 / var(--tw-border-opacity));
  padding: 20px 18px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-title:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-title .avtar i{
  font-size: 18px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-title h5{
  font-weight: 600;
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-list{
  height: calc(100vh - 74px - 80px);
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-list .simplebar-content > .pc-submenu > .pc-item::before{
  content: var(--tw-content);
  left: 15px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-list .simplebar-content > .pc-submenu > .pc-item > .pc-link{
  padding: 10px 16px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-list .simplebar-content > .pc-submenu > .pc-item > .pc-submenu > .pc-item::before{
  content: var(--tw-content);
  left: 30px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-list .simplebar-content > .pc-submenu > .pc-item > .pc-submenu > .pc-item > .pc-link{
  padding: 10px 16px 10px 30px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-list .simplebar-content > .pc-submenu > .pc-item > .pc-submenu > .pc-item > .pc-submenu > .pc-item::before{
  content: var(--tw-content);
  left: 45px;
}
[data-pc-layout="compact"] .pc-sidebar .pc-compact-submenu .pc-compact-list .simplebar-content > .pc-submenu > .pc-item > .pc-submenu > .pc-item > .pc-submenu > .pc-item > .pc-link{
  padding: 10px 16px 10px 45px;
}
[data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active .navbar-content{
  width: 300px;
}
[data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active .navbar-content::before{
  position: absolute;
  top: 0px;
  left: 92px;
  height: 100%;
  width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(231 234 238 / var(--tw-bg-opacity));
  --tw-content: "";
  content: var(--tw-content);
}
[data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active .navbar-content:is([data-pc-theme="dark"] *)::before{
  --tw-bg-opacity: 1;
  background-color: rgb(48 63 80 / var(--tw-bg-opacity));
}
[data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active .navbar-wrapper{
  display: flex !important;
  width: 300px;
  flex-wrap: wrap;
}
[data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active .navbar-wrapper .m-header{
  width: 100%;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-bottom-color: rgb(231 234 238 / var(--tw-border-opacity));
}
[data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active .navbar-wrapper .m-header:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
}
[data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active .navbar-wrapper .pc-compact-submenu{
  flex: 1 1 0%;
  width: calc(100% - 100px);
}
[data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active .navbar-wrapper .pc-compact-submenu .pc-compact-list .simplebar-content > .pc-submenu{
  display: block !important;
}
@media (min-width: 1025px){
  [data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active ~ .pc-footer,[data-pc-layout="compact"] .pc-sidebar.pc-compact-submenu-active ~ .pc-container{
    margin-left: 300px;
  }
}
[data-pc-layout="compact"] .pc-sidebar.pc-sidebar-hide{
  width: 0px;
}
[data-pc-layout="compact"] .pc-sidebar.pc-sidebar-hide ~ .pc-footer, [data-pc-layout="compact"] .pc-sidebar.pc-sidebar-hide ~ .pc-container{
  margin-left: 0px;
}
[data-pc-layout="compact"] .pc-sidebar.pc-sidebar-hide .pc-navbar > .pc-item.pc-hasmenu.pc-trigger > > .pc-submenu{
  display: none !important;
}
[data-pc-layout="compact"] .pc-sidebar .pc-badge,[data-pc-layout="compact"] .pc-sidebar .pc-caption,[data-pc-layout="compact"] .pc-sidebar .pc-mtext,[data-pc-layout="compact"] .pc-sidebar .pc-navbar > li > a > .pc-arrow{
  display: none;
}
@media (min-width: 1025px){
  [data-pc-layout="compact"] .pc-header{
    left: 100px;
  }
}
[data-pc-layout="compact"] .pc-sidebar-hide .pc-header{
  left: 0px;
}
@media (min-width: 1025px){
  [data-pc-layout="compact"] .pc-compact-submenu-active.pc-sidebar-hide .pc-header{
    left: 0px;
  }
  [data-pc-layout="compact"] .pc-compact-submenu-active .pc-header{
    left: 300px;
  }
}
[data-pc-layout="tab"] .pc-sidebar .tab-container{
  display: flex;
  align-items: flex-start;
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu, [data-pc-layout="tab"] .pc-sidebar .tab-container .tab-link{
  height: calc(100vh - 74px);
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu{
  width: 75px;
  border-right-width: 1px;
  --tw-border-opacity: 1;
  border-right-color: rgb(231 234 238 / var(--tw-border-opacity));
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-right-color: rgb(48 63 80 / var(--tw-border-opacity));
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu .nav-link{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  height: 50px;
  width: 50px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 0px;
  font-size: 18px;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu .nav-link:hover{
  background-color: rgb(91 107 121 / 0.2);
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu .nav-link:is([data-pc-theme="dark"] *){
  color: rgba(255, 255, 255, 0.5);
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu .nav-link:hover:is([data-pc-theme="dark"] *){
  background-color: rgba(255, 255, 255, 0.2);
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu .nav-link svg{
  height: 22px;
  width: 22px;
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu .nav-link:focus,[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-sidemenu .nav-link.active{
  background-color: rgb(var(--colors-primary-500) / 0.2);
  color: rgb(var(--colors-primary-500));
}
[data-pc-layout="tab"] .pc-sidebar .tab-container .tab-link{
  width: 1%;
  min-width: 0px;
  flex: 1 1 auto;
}
[data-pc-layout="tab"] .pc-sidebar .pc-caption, [data-pc-layout="tab"] .pc-sidebar .pc-user-card{
  display: none;
}
[data-pc-layout="tab"] .pc-sidebar .pc-mtext{
  margin-left: 0.625rem;
}
[data-pc-layout="tab"] .pc-sidebar .pc-micon{
  margin-right: 0px;
}
[data-pc-layout="tab"] .pc-sidebar:not(.pc-sidebar-hide){
  width: 320px;
}
[data-pc-layout="tab"] .pc-sidebar:not(.pc-sidebar-hide) .navbar-wrapper{
  width: 320px;
}
[data-pc-layout="tab"] .pc-sidebar .pc-navbar > .pc-item > .pc-submenu .pc-link{
  padding: 12px 30px 12px 45px;
}
[data-pc-layout="tab"] .pc-sidebar .pc-navbar > .pc-item > .pc-submenu .pc-link::after{
  content: var(--tw-content);
  left: 30px;
}
[data-pc-layout="tab"] .pc-sidebar .pc-navbar > .pc-item .pc-submenu .pc-submenu .pc-link{
  padding: 12px 30px 12px 52px;
}
[data-pc-layout="tab"] .pc-sidebar .pc-navbar > .pc-item .pc-submenu .pc-submenu .pc-link::after{
  content: var(--tw-content);
  left: 40px;
}
[data-pc-layout="tab"] .pc-sidebar .pc-navbar > .pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link{
  padding: 12px 30px 12px 70px;
}
[data-pc-layout="tab"] .pc-sidebar .pc-navbar > .pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link::after{
  content: var(--tw-content);
  left: 52px;
}
@media (min-width: 1025px){
  [data-pc-layout="tab"] .pc-sidebar:not(.pc-sidebar-hide) ~ .pc-header{
    left: 320px;
  }
  [data-pc-layout="tab"] .pc-sidebar:not(.pc-sidebar-hide) ~ .pc-container, [data-pc-layout="tab"] .pc-sidebar:not(.pc-sidebar-hide) ~ .pc-footer{
    margin-left: 320px;
  }
}
@media (max-width: 1024px){
  [data-pc-layout="tab"] .pc-sidebar:not(.mob-sidebar-active){
    left: -320px;
  }
}
.preset-btn.btn{
  display: flex;
  height: 60px;
  width: 100%;
  align-items: center;
  justify-content: center;
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  padding: 5px;
}
.preset-btn.btn:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.preset-btn.btn.active{
  border-color: rgb(var(--colors-primary-500));
}
.preset-btn.btn.btn-img{
  height: auto;
  border-radius: 0.5rem;
}
.preset-btn.btn.btn-img img{
  width: 100%;
}
.theme-color{
  position: relative;
  display: flex;
  gap: 0.25rem;
}
.theme-color >a{
  position: relative;
  display: inline-flex;
  height: 2.5rem;
  width: 2.5rem;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 0.75rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.theme-color >a:after{
  position: absolute;
  inset: 4px;
  z-index: 10;
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 0.5rem;
  background-color: rgb(255 255 255 / 0.3);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-content: "";
  content: var(--tw-content);
}
.theme-color >a i{
  font-size: 20px;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  opacity: 0.7;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.theme-color >a:hover i{
  opacity: 1;
}
.theme-color >a.active:after{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.theme-color >a.active i{
  opacity: 1;
}
.accordion{
  overflow: hidden;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.accordion:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.accordion.accordion-flush{
  border-width: 0px;
}
.accordion .accordion-item + .accordion-item{
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.accordion .accordion-item + .accordion-item:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.accordion .accordion-header.card-header{
  position: relative;
  width: 100%;
  padding-top: 1rem;
  padding-bottom: 1rem;
  text-align: start;
}
.accordion .accordion-header.card-header::before{
  position: absolute;
  font-family: 'tabler-icons';
  font-size: 1.125rem;
  line-height: 1.75rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
  transition-timing-function: cubic-bezier(.47,1.64,.41,.8);
  --tw-content: '\ea5f';
  content: var(--tw-content);
}
.accordion .accordion-header.card-header:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 2.5rem;
}
.accordion .accordion-header.card-header:where([dir="ltr"], [dir="ltr"] *)::before{
  content: var(--tw-content);
  right: 25px;
}
.accordion .accordion-header.card-header:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 2.5rem;
}
.accordion .accordion-header.card-header:where([dir="rtl"], [dir="rtl"] *)::before{
  content: var(--tw-content);
  left: 25px;
}
.accordion .accordion-header.card-header.show{
  background-color: rgb(var(--colors-primary-500) / 0.1);
  color: rgb(var(--colors-primary-500));
}
.accordion .accordion-header.card-header.show::before{
  content: var(--tw-content);
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.show:not([data-pc-toggle="collapse"]){
  display: block !important;
}
.alert{
  margin-bottom: 0.75rem;
  border-radius: 0.5rem;
  border-width: 1px;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.alert .alert-link{
  font-weight: 700;
}
.alert .alert-heading{
  color: inherit;
}
.alert hr{
  border-color: inherit;
}
.alert.alert-dismissible{
  position: relative;
  padding-right: 2.5rem;
}
.alert.alert-dismissible:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 3rem;
  padding-right: 1.25rem;
}
.alert.alert-dismissible [data-pc-dismiss="alert"]{
  position: absolute;
  top: 0.5rem;
}
.alert.alert-dismissible [data-pc-dismiss="alert"]:where([dir="ltr"], [dir="ltr"] *){
  right: 0.5rem;
}
.alert.alert-dismissible [data-pc-dismiss="alert"]:where([dir="rtl"], [dir="rtl"] *){
  left: 0.5rem;
}
.alert-primary{
  border-color: rgb(var(--colors-primary-500) / 0.2);
  background-color: rgb(var(--colors-primary-500) / 0.2);
  color: rgb(var(--colors-primary-800));
}
.alert-secondary{
  border-color: rgb(91 107 121 / 0.2);
  background-color: rgb(91 107 121 / 0.2);
  --tw-text-opacity: 1;
  color: rgb(38 45 51 / var(--tw-text-opacity));
}
.alert-secondary:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(190 198 206 / var(--tw-text-opacity));
}
.alert-success{
  border-color: rgb(44 168 127 / 0.1);
  background-color: rgb(44 168 127 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(27 141 98 / var(--tw-text-opacity));
}
.alert-danger{
  border-color: rgb(220 38 38 / 0.2);
  background-color: rgb(220 38 38 / 0.2);
  --tw-text-opacity: 1;
  color: rgb(206 23 23 / var(--tw-text-opacity));
}
.alert-warning{
  border-color: rgb(229 138 0 / 0.2);
  background-color: rgb(229 138 0 / 0.2);
  --tw-text-opacity: 1;
  color: rgb(218 109 0 / var(--tw-text-opacity));
}
.alert-info{
  border-color: rgb(62 201 214 / 0.2);
  background-color: rgb(62 201 214 / 0.2);
  --tw-text-opacity: 1;
  color: rgb(40 181 198 / var(--tw-text-opacity));
}
.alert-dark{
  border-color: rgb(33 37 41 / 0.2);
  background-color: rgb(33 37 41 / 0.2);
  --tw-text-opacity: 1;
  color: rgb(13 14 16 / var(--tw-text-opacity));
}
.alert-dark:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(158 167 177 / var(--tw-text-opacity));
}
.badge{
  display: inline-block;
  border-radius: 0.375rem;
  padding-left: 0.8em;
  padding-right: 0.8em;
  padding-top: 0.45em;
  padding-bottom: 0.45em;
  font-size: .75em;
  font-weight: 500;
  line-height: 0.9;
}
.btn{
  display: inline-block;
  border-radius: 20px;
  border-width: 1px;
  border-color: transparent;
  background-color: transparent;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.btn.disabled{
  pointer-events: none;
  cursor: default;
  opacity: 0.75;
}
.btn.btn-lg{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.btn.btn-sm{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.btn.btn-icon{
  display: inline-flex;
  height: 2.5rem;
  width: 2.5rem;
  align-items: center;
  justify-content: center;
  padding: 0px;
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.btn.btn-icon.avtar-xl{
  height: 60px;
  width: 60px;
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.btn.btn-icon.avtar-l{
  height: 50px;
  width: 50px;
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.btn.btn-icon.avtar-s{
  height: 30px;
  width: 30px;
  font-size: 0.875rem;
}
.btn.btn-icon.avtar-xs{
  height: 1.25rem;
  width: 1.25rem;
  font-size: 0.75rem;
}
.btn-pc-default:not(:hover){
  color: rgb(19 25 32 / 0.7);
}
.btn-pc-default:not(:hover):is([data-pc-theme="dark"] *){
  color: rgb(191 191 191 / 0.7);
}
.btn-primary{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-primary:hover{
  background-color: rgb(var(--colors-primary-600));
}
.btn-primary:focus{
  background-color: rgb(var(--colors-primary-600));
}
.btn-primary:active{
  background-color: rgb(var(--colors-primary-700));
}
.btn-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-secondary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
}
.btn-secondary:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
}
.btn-secondary:active{
  --tw-bg-opacity: 1;
  background-color: rgb(56 66 74 / var(--tw-bg-opacity));
}
.btn-success{
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-success:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(39 160 119 / var(--tw-bg-opacity));
}
.btn-success:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(39 160 119 / var(--tw-bg-opacity));
}
.btn-success:active{
  --tw-bg-opacity: 1;
  background-color: rgb(33 151 108 / var(--tw-bg-opacity));
}
.btn-danger{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-danger:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(216 34 34 / var(--tw-bg-opacity));
}
.btn-danger:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(216 34 34 / var(--tw-bg-opacity));
}
.btn-danger:active{
  --tw-bg-opacity: 1;
  background-color: rgb(211 28 28 / var(--tw-bg-opacity));
}
.btn-warning{
  --tw-bg-opacity: 1;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-warning:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(226 130 0 / var(--tw-bg-opacity));
}
.btn-warning:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(226 130 0 / var(--tw-bg-opacity));
}
.btn-warning:active{
  --tw-bg-opacity: 1;
  background-color: rgb(222 119 0 / var(--tw-bg-opacity));
}
.btn-info{
  --tw-bg-opacity: 1;
  background-color: rgb(62 201 214 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-info:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(56 195 209 / var(--tw-bg-opacity));
}
.btn-info:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(56 195 209 / var(--tw-bg-opacity));
}
.btn-info:active{
  --tw-bg-opacity: 1;
  background-color: rgb(48 188 204 / var(--tw-bg-opacity));
}
.btn-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity));
}
.btn-dark:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity));
}
.btn-dark:active{
  --tw-bg-opacity: 1;
  background-color: rgb(19 22 24 / var(--tw-bg-opacity));
}
.btn-light{
  background-color: rgb(242 244 245 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.btn-light:hover{
  background-color: rgb(242 244 245 / 0.2);
}
.btn-light:focus{
  background-color: rgb(242 244 245 / 0.2);
}
.btn-light:active{
  background-color: rgb(242 244 245 / 0.3);
}
.btn-light:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-link{
  background-color: transparent;
  color: rgb(var(--colors-primary-500));
}
.btn-link:hover{
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.btn-link:focus{
  background-color: rgb(var(--colors-primary-50) / 0.2);
}
.btn-link:active{
  background-color: rgb(var(--colors-primary-50) / 0.3);
}
.btn-outline-primary{
  border-width: 1px;
  border-color: rgb(var(--colors-primary));
  background-color: rgb(var(--colors-primary-500) / 0);
  color: rgb(var(--colors-primary-500));
}
.btn-outline-primary:hover{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-primary:focus{
  background-color: rgb(var(--colors-primary-600));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-secondary{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(91 107 121 / var(--tw-border-opacity));
  background-color: rgb(91 107 121 / 0);
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.btn-outline-secondary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-secondary:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-success{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(44 168 127 / var(--tw-border-opacity));
  background-color: rgb(44 168 127 / 0);
  --tw-text-opacity: 1;
  color: rgb(44 168 127 / var(--tw-text-opacity));
}
.btn-outline-success:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-success:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(39 160 119 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-danger{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
  background-color: rgb(220 38 38 / 0);
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.btn-outline-danger:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-danger:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(216 34 34 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-warning{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 138 0 / var(--tw-border-opacity));
  background-color: rgb(229 138 0 / 0);
  --tw-text-opacity: 1;
  color: rgb(229 138 0 / var(--tw-text-opacity));
}
.btn-outline-warning:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-warning:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(226 130 0 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-info{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(62 201 214 / var(--tw-border-opacity));
  background-color: rgb(62 201 214 / 0);
  --tw-text-opacity: 1;
  color: rgb(62 201 214 / var(--tw-text-opacity));
}
.btn-outline-info:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(62 201 214 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-info:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(56 195 209 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-dark{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(33 37 41 / var(--tw-border-opacity));
  background-color: rgb(33 37 41 / 0);
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.btn-outline-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-dark:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-dark:is([data-pc-theme="dark"] *){
  border-color: rgb(255 255 255 / 0.5);
  color: rgb(255 255 255 / 0.5);
}
.btn-light-primary{
  background-color: rgb(var(--colors-primary-100));
  color: rgb(var(--colors-primary-500));
}
.btn-light-primary:hover{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-primary:focus{
  background-color: rgb(var(--colors-primary-600));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-primary:is([data-pc-theme="dark"] *){
  background-color: rgb(var(--colors-primary-500) / 0.1);
}
.btn-light-primary:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(var(--colors-primary-500));
}
.btn-light-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(225 229 232 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.btn-light-secondary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-secondary:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-secondary:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.btn-light-secondary:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.btn-light-success{
  --tw-bg-opacity: 1;
  background-color: rgb(192 229 217 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(44 168 127 / var(--tw-text-opacity));
}
.btn-light-success:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-success:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(39 160 119 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-success:is([data-pc-theme="dark"] *){
  background-color: rgb(44 168 127 / 0.1);
}
.btn-light-success:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
}
.btn-light-danger{
  --tw-bg-opacity: 1;
  background-color: rgb(245 190 190 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.btn-light-danger:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-danger:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(216 34 34 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-danger:is([data-pc-theme="dark"] *){
  background-color: rgb(220 38 38 / 0.1);
}
.btn-light-danger:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
.btn-light-warning{
  --tw-bg-opacity: 1;
  background-color: rgb(247 220 179 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(229 138 0 / var(--tw-text-opacity));
}
.btn-light-warning:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-warning:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(226 130 0 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-warning:is([data-pc-theme="dark"] *){
  background-color: rgb(229 138 0 / 0.1);
}
.btn-light-warning:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity));
}
.btn-light-info{
  --tw-bg-opacity: 1;
  background-color: rgb(197 239 243 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(62 201 214 / var(--tw-text-opacity));
}
.btn-light-info:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(62 201 214 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-info:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(56 195 209 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-info:is([data-pc-theme="dark"] *){
  background-color: rgb(62 201 214 / 0.1);
}
.btn-light-info:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(62 201 214 / var(--tw-bg-opacity));
}
.btn-light-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(206 210 215 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.btn-light-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-dark:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-dark:is([data-pc-theme="dark"] *){
  background-color: rgb(33 37 41 / 0.1);
  color: rgb(255 255 255 / 0.8);
}
.btn-light-dark:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.btn-link-primary{
  background-color: transparent;
  color: rgb(var(--colors-primary-500));
}
.btn-link-primary:hover{
  background-color: rgb(var(--colors-primary-100));
}
.btn-link-primary:focus{
  background-color: rgb(var(--colors-primary-100) / 0.5);
}
.btn-link-secondary{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.btn-link-secondary:hover{
  background-color: rgb(225 229 232 / 0.5);
}
.btn-link-secondary:focus{
  background-color: rgb(190 198 206 / 0.5);
}
.btn-link-secondary:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.btn-link-secondary:focus:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.btn-link-success{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(44 168 127 / var(--tw-text-opacity));
}
.btn-link-success:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(192 229 217 / var(--tw-bg-opacity));
}
.btn-link-success:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(150 212 191 / var(--tw-bg-opacity));
}
.btn-link-danger{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.btn-link-danger:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(245 190 190 / var(--tw-bg-opacity));
}
.btn-link-danger:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(238 147 147 / var(--tw-bg-opacity));
}
.btn-link-warning{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(229 138 0 / var(--tw-text-opacity));
}
.btn-link-warning:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(247 220 179 / var(--tw-bg-opacity));
}
.btn-link-warning:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(242 197 128 / var(--tw-bg-opacity));
}
.btn-link-info{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(62 201 214 / var(--tw-text-opacity));
}
.btn-link-info:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(197 239 243 / var(--tw-bg-opacity));
}
.btn-link-info:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(159 228 235 / var(--tw-bg-opacity));
}
.btn-link-dark{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.btn-link-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(206 210 215 / var(--tw-bg-opacity));
}
.btn-link-dark:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(158 167 177 / var(--tw-bg-opacity));
}
.btn-link-dark:is([data-pc-theme="dark"] *){
  color: rgb(255 255 255 / 0.8);
}
.btn-link-dark:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(33 37 41 / 0.1);
}
.introjs-tooltipbuttons [role="button"]{
  display: inline-block;
  border-radius: 20px;
  border-width: 1px;
  border-color: transparent;
  background-color: transparent;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: none;
}
.introjs-tooltipbuttons [role="button"].introjs-prevbutton{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-prevbutton:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-prevbutton:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-prevbutton:active{
  --tw-bg-opacity: 1;
  background-color: rgb(56 66 74 / var(--tw-bg-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-nextbutton{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-nextbutton:hover{
  background-color: rgb(var(--colors-primary-600));
}
.introjs-tooltipbuttons [role="button"].introjs-nextbutton:focus{
  background-color: rgb(var(--colors-primary-600));
}
.introjs-tooltipbuttons [role="button"].introjs-nextbutton:active{
  background-color: rgb(var(--colors-primary-700));
}
.breadcrumb{
  margin-bottom: 1rem;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  gap: 0.5rem;
}
.breadcrumb .breadcrumb-item:last-child{
  opacity: 0.75;
}
.breadcrumb .breadcrumb-item:not(:last-child){
  position: relative;
}
.breadcrumb .breadcrumb-item:not(:last-child)::before{
  position: absolute;
  content: var(--tw-content);
  font-family: 'tabler-icons';
}
.breadcrumb .breadcrumb-item:not(:last-child):where([dir="ltr"], [dir="ltr"] *){
  margin-right: 1.35rem;
}
.breadcrumb .breadcrumb-item:not(:last-child):where([dir="ltr"], [dir="ltr"] *)::before{
  right: -20px;
  --tw-content: '\ea61';
  content: var(--tw-content);
}
.breadcrumb .breadcrumb-item:not(:last-child):where([dir="rtl"], [dir="rtl"] *){
  margin-left: 1.35rem;
}
.breadcrumb .breadcrumb-item:not(:last-child):where([dir="rtl"], [dir="rtl"] *)::before{
  left: -20px;
  --tw-content: '\ea60';
  content: var(--tw-content);
}
.breadcrumb .breadcrumb-item:not(:last-child) a{
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.breadcrumb .breadcrumb-item:not(:last-child) a:hover{
  color: rgb(var(--colors-primary-500));
}
.card{
  position: relative;
  margin-bottom: 1.5rem;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.card:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.card .card-header{
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  padding: 25px;
}
.card .card-header:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.card .card-body{
  padding: 25px;
}
.card .card-footer{
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  padding: 25px;
}
.card .card-footer:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.card .card-link{
  margin-right: 0.75rem;
  display: inline-block;
  color: rgb(var(--colors-primary-500));
}
.card .card-link:hover{
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.card .card-link:hover:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.choices{
  position: relative;
}
.choices:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.choices:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.choices [hidden]{
  display: none;
}
.choices[data-type*=select-multiple] .choices__button, .choices[data-type*=text] .choices__button{
  position: relative;
  margin-top: 0px;
  margin-bottom: 0px;
  margin-left: 0.5rem;
  margin-right: -0.25rem;
  display: inline-block;
  width: 0.5rem;
  border-left-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);
  background-size: 8px 8px;
  background-position: center;
  background-repeat: no-repeat;
  padding-left: 1rem;
  text-indent: -9999px;
  line-height: 1;
  opacity: 0.75;
}
.choices.is-disabled .choices__inner, .choices.is-disabled .choices__input{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  opacity: 0.5;
}
.choices[data-type*=select-one]::after{
  position: absolute;
  right: 11.5px;
  top: 50%;
  height: 0px;
  width: 0px;
  border-width: 5px;
  border-color: #131920 transparent transparent;
  --tw-content: "";
  content: var(--tw-content);
}
.choices[data-type*=select-one]:is([data-pc-theme="dark"] *)::after{
  content: var(--tw-content);
  border-color: #bfbfbf transparent transparent;
}
.choices[data-type*=select-one] .choices__input{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  opacity: 0.5;
}
.choices__list{
  margin: 0px;
  list-style-type: none;
  padding-left: 0px;
}
.choices__input{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
  margin-bottom: 0px;
  max-width: 100%;
  border-radius: 0px;
  border-width: 0px;
  background-color: transparent !important;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.125rem;
  padding-right: 0px;
  vertical-align: baseline;
}
.choices__input:focus{
  outline-width: 0px;
}
.choices__inner{
  display: inline-block;
  min-height: 44px;
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 7.5px;
  padding-right: 7.5px;
  padding-top: 7.5px;
  padding-bottom: 3.75px;
  vertical-align: top;
}
.choices__inner:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.choices__inner:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.choices__inner:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.choices__list--single{
  display: inline-block;
  width: 100%;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.25rem;
  padding-right: 1rem;
}
.choices__list--multiple{
  display: inline-block;
}
.choices__list--multiple .choices__item{
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
  display: inline-block;
  border-radius: 0.5rem;
  border-color: rgb(var(--colors-primary-500));
  background-color: rgb(var(--colors-primary-500));
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  vertical-align: middle;
  font-size: 0.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.choices__list--dropdown{
  visibility: hidden;
  position: absolute;
  top: 100%;
  z-index: 10;
  margin-top: -1px;
  width: 100%;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.choices__list--dropdown:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.choices__list--dropdown.is-active{
  visibility: visible;
}
.choices__list--dropdown .choices__item{
  position: relative;
  padding: 0.625rem;
  font-size: 0.875rem;
}
.choices__list--dropdown .choices__list{
  position: relative;
  max-height: 300px;
  overflow: auto;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.btn-group, .dropdown{
  position: relative;
}
.btn-group .dropdown-toggle, .dropdown .dropdown-toggle{
  position: relative;
}
.btn-group .dropdown-toggle::after, .dropdown .dropdown-toggle::after{
  vertical-align: bottom;
  font-family: 'tabler-icons';
  font-size: 0.875rem;
  --tw-content: '\ea5f';
  content: var(--tw-content);
}
.btn-group .dropdown-toggle.arrow-none::after, .dropdown .dropdown-toggle.arrow-none::after{
  content: var(--tw-content);
  display: none;
}
.btn-group .dropdown-menu, .dropdown .dropdown-menu{
  position: absolute;
  left: 0px;
  top: 100%;
  min-width: 12rem;
  transform-origin: top left;
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
  opacity: 0;
  --tw-shadow: 0 4px 24px 0 rgba(62,57,107,.18);
  --tw-shadow-colored: 0 4px 24px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.btn-group .dropdown-menu:is([data-pc-theme="dark"] *), .dropdown .dropdown-menu:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(38 50 64 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.btn-group .dropdown-menu.dropdown-menu-end, .dropdown .dropdown-menu.dropdown-menu-end{
  right: 0px;
  left: auto;
}
.btn-group .dropdown-menu .dropdown-item, .dropdown .dropdown-menu .dropdown-item{
  display: block;
  display: flex;
  width: 100%;
  align-items: center;
  gap: 0.75rem;
  border-radius: 0.5rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  padding-left: 15px;
  padding-right: 15px;
}
.btn-group .dropdown-menu .dropdown-item:hover, .dropdown .dropdown-menu .dropdown-item:hover{
  background-color: rgb(225 229 232 / 0.2);
}
.btn-group .dropdown-menu .dropdown-item i, .dropdown .dropdown-menu .dropdown-item i{
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 1;
}
.btn-group .dropdown-menu .dropdown-item svg, .dropdown .dropdown-menu .dropdown-item svg{
  height: 18px;
  width: 18px;
}
.btn-group .dropdown-menu[data-popper-reference-hidden], .dropdown .dropdown-menu[data-popper-reference-hidden]{
  display: none;
}
.btn-group:not(.drp-show) .dropdown-menu, .dropdown:not(.drp-show) .dropdown-menu{
  z-index: -10;
}
.btn-group.drp-show .dropdown-menu, .dropdown.drp-show .dropdown-menu{
  z-index: 50;
  display: block;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transform: none;
  opacity: 1;
}
.form-control,.datatable-input{
  display: block;
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: .8rem;
  padding-bottom: .8rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
}
.form-control::-moz-placeholder, .datatable-input::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.form-control::placeholder,.datatable-input::placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.form-control:focus,.datatable-input:focus{
  border-color: rgb(var(--colors-primary-500));
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.form-control:disabled,.datatable-input:disabled{
  pointer-events: none;
  background-color: rgb(190 198 206 / 0.1);
}
.form-control:is([data-pc-theme="dark"] *),.datatable-input:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(38 50 64 / var(--tw-bg-opacity));
}
.form-control:focus:is([data-pc-theme="dark"] *),.datatable-input:focus:is([data-pc-theme="dark"] *){
  border-color: rgb(var(--colors-primary-500));
}
.form-control[type="file"]::file-selector-button, .datatable-input[type="file"]::file-selector-button{
  margin-top: -.8rem;
  margin-bottom: -.8rem;
  margin-left: -0.75rem;
  margin-right: -0.75rem;
  margin-inline-end: 0.75rem;
  cursor: pointer;
  border-width: 0px;
  border-right-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  background-color: rgb(155 168 180 / 0.1);
  padding-top: .8rem;
  padding-bottom: .8rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.form-control[type="file"]:is([data-pc-theme="dark"] *)::file-selector-button, .datatable-input[type="file"]:is([data-pc-theme="dark"] *)::file-selector-button{
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.form-control.error, .datatable-input.error{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}
.form-control-plaintext{
  display: block;
  width: 100%;
  border-radius: 0.5rem;
  background-color: transparent;
  padding-top: .8rem;
  padding-bottom: .8rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
}
.form-control-plaintext::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.form-control-plaintext::placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.form-control-plaintext:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.form-control-plaintext:disabled{
  pointer-events: none;
}
.form-control-plaintext:is([data-pc-theme="dark"] *){
  background-color: transparent;
}
.form-control-lg{
  border-radius: 10px;
  padding-top: .775rem;
  padding-bottom: .775rem;
  padding-left: .85rem;
  padding-right: .85rem;
  font-size: 1.09375rem;
}
.form-control-sm{
  border-radius: 0.375rem;
  padding-top: .375rem;
  padding-bottom: .375rem;
  padding-left: .7rem;
  padding-right: .7rem;
  font-size: .765625rem;
}
.form-select, .datatable-selector{
  display: block;
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%231d2630' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-size: 16px 12px;
  background-repeat: no-repeat;
  padding-top: .8rem;
  padding-bottom: .8rem;
  padding-left: 0.75rem;
  padding-right: 2rem;
  font-size: 0.875rem;
}
.form-select:focus, .datatable-selector:focus{
  border-color: rgb(var(--colors-primary-500));
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.form-select:is([data-pc-theme="dark"] *), .datatable-selector:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(38 50 64 / var(--tw-bg-opacity));
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23bfbfbf' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
.form-select:focus:is([data-pc-theme="dark"] *), .datatable-selector:focus:is([data-pc-theme="dark"] *){
  border-color: rgb(var(--colors-primary-500));
}
.form-select:where([dir="ltr"], [dir="ltr"] *), .datatable-selector:where([dir="ltr"], [dir="ltr"] *){
  background-position: right 1rem center;
}
.form-select:where([dir="rtl"], [dir="rtl"] *), .datatable-selector:where([dir="rtl"], [dir="rtl"] *){
  background-position: left 1rem center;
}
.form-select.error, .datatable-selector.error{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}
.form-select[multiple], .form-select[size]:not([size="1"]), [multiple].datatable-selector, [size].datatable-selector:not([size="1"]){
  background-image: none;
  padding-right: 0.75rem;
}
.form-select-lg{
  border-radius: 10px;
  padding-top: .775rem;
  padding-bottom: .775rem;
  padding-left: .85rem;
  padding-right: .85rem;
  font-size: 1.09375rem;
}
.form-select-sm{
  border-radius: 6px;
  padding-top: .375rem;
  padding-bottom: .375rem;
  padding-left: .7rem;
  font-size: .765625rem;
}
.form-control-color{
  height: calc(1.5em + 1.6rem + 2px);
  width: 3rem;
  padding: .8rem;
}
.form-control-color::-moz-color-swatch, .form-control-color::-webkit-color-swatch{
  border-radius: 0.5rem !important;
  border-width: 0px !important;
}
.input-group-text{
  display: flex;
  align-items: center;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  padding: .8rem .75rem;
  font-size: 0.875rem;
}
.input-group-text:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.input-group{
  position: relative;
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  align-items: stretch;
}
.input-group > *{
  border-radius: 0px;
}
.input-group > *:where([dir="ltr"], [dir="ltr"] *){
  margin-left: -1px;
}
.input-group > *:first-child:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.input-group > *:last-child:where([dir="ltr"], [dir="ltr"] *){
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.input-group > *:where([dir="rtl"], [dir="rtl"] *){
  margin-right: -1px;
}
.input-group > *:first-child:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px;
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.input-group > *:last-child:where([dir="rtl"], [dir="rtl"] *){
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.input-group .form-control, .input-group .form-select{
  position: relative;
  width: 1%;
  min-width: 0px;
  flex: 1 1 auto;
}
.datepicker-cell.selected{
  background-color: rgb(var(--colors-primary-500));
}
.datepicker-cell.selected:hover{
  background-color: rgb(var(--colors-primary-500));
}
.datepicker-cell.today.focused:not(.selected){
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
}
.datepicker-cell.today.focused:not(.selected):hover{
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
}
.datepicker-view .week{
  color: rgb(var(--colors-primary-500));
}
.form-label{
  margin-bottom: 0.5rem;
  display: inline-block;
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.form-label:is([data-pc-theme="dark"] *){
  color: rgba(255, 255, 255, 0.8);
}
.col-form-label{
  padding-top: calc(.8rem + 1px);
  padding-bottom: calc(.8rem + 1px);
}
.col-form-label-sm{
  padding-top: calc(.375rem + 1px);
  padding-bottom: calc(.375rem + 1px);
}
.col-form-label-lg{
  padding-top: calc(.775rem + 1px);
  padding-bottom: calc(.775rem + 1px);
}
.form-check-input{
  height: 1.25em;
  width: 1.25em;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  vertical-align: text-top;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.form-check-input:checked{
  border-color: rgb(var(--colors-primary-500));
  background-color: rgb(var(--colors-primary-500));
}
.form-check-input:disabled{
  pointer-events: none;
  opacity: 0.5;
}
.form-check-input:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.form-check-input[disabled] ~.form-check-label, .form-check-input:disabled ~.form-check-label{
  cursor: default;
  opacity: 0.5;
}
.form-check-input.error{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}
.form-check-input[type="checkbox"]{
  border-radius: 0.375rem;
}
.form-check-input[type="checkbox"]:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.form-check-input[type="radio"]{
  border-radius: 9999px;
}
.form-check-input[type="radio"]:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e");
}
.form-check.form-switch{
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.form-check.form-switch .form-check-input{
  width: 2em;
  border-radius: 9999px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  background-position: left;
}
.form-check.form-switch .form-check-input:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
  background-position: right;
}
.form-range{
  height: 0.5rem;
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
}
.form-range:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.form-range::-webkit-slider-thumb{
  height: 1rem;
  width: 1rem;
  cursor: pointer;
  -webkit-appearance: none;
          appearance: none;
  border-radius: 9999px;
  background-color: rgb(var(--colors-primary-500));
}
.form-range:focus::-webkit-slider-thumb{
  opacity: 0.9;
}
.pc-toggle-noUiSlider{
  height: 50px;
}
.pc-toggle-noUiSlider.off .noUi-handle{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.noUi-target{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
}
.noUi-target:is([data-pc-theme="dark"] *){
  border-width: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.noUi-handle{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.noUi-handle:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.CodeMirror, .editor-toolbar{
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.CodeMirror:is([data-pc-theme="dark"] *), .editor-toolbar:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.CodeMirror a, .editor-toolbar a{
  border-width: 0px;
  --tw-text-opacity: 1 !important;
  color: rgb(19 25 32 / var(--tw-text-opacity)) !important;
}
.CodeMirror a:is([data-pc-theme="dark"] *), .editor-toolbar a:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
.CodeMirror a.active, .CodeMirror a:hover, .editor-toolbar a.active, .editor-toolbar a:hover{
  background-color: rgba(0,0,0,0.1);
  --tw-text-opacity: 1 !important;
  color: rgb(19 25 32 / var(--tw-text-opacity)) !important;
}
.CodeMirror a.active:is([data-pc-theme="dark"] *), .CodeMirror a:hover:is([data-pc-theme="dark"] *), .editor-toolbar a.active:is([data-pc-theme="dark"] *), .editor-toolbar a:hover:is([data-pc-theme="dark"] *){
  background-color: rgba(0,0,0,0.2);
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
.CodeMirror i.separator, .editor-toolbar i.separator{
  border-right-color: transparent;
  --tw-border-opacity: 1;
  border-left-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.CodeMirror i.separator:is([data-pc-theme="dark"] *), .editor-toolbar i.separator:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-left-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.noUi-connect{
  background-color: rgb(var(--colors-primary-500));
}
.switch-handle{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.switch-handle:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.code-input::-webkit-outer-spin-button, .code-input::-webkit-inner-spin-button{
  -webkit-appearance: none;
  margin: 0px;
}
.code-input[type="number"]{
  -moz-appearance: textfield;
}
.typeahead{
  position: relative;
}
.typeahead>ul{
  position: absolute;
  top: 100%;
  left: 0px;
  float: left;
  margin: 2px 0 0;
  min-width: 170px;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 5px -;
  --tw-shadow: 0 6px 12px rgba(0,0,0,.17);
  --tw-shadow-colored: 0 6px 12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.typeahead>ul:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.typeahead>ul>li >a{
  display: block;
  white-space: nowrap;
  padding: 3px 20px;
  line-height: 1.5;
}
.typeahead>ul>li.active>a, .typeahead>ul>li.active>a:hover, .typeahead>ul>li>a:hover{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}
#cke5-inline-demo .ck-content{
  margin-bottom: 1rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 1rem;
}
#cke5-inline-demo .ck-content:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
@media (min-width: 640px){
  #cke5-inline-demo .ck-content{
    padding: 2.5rem;
  }
}
#cke5-inline-demo .ck-content .image-inline{
  float: right;
  margin-left: var(--ck-image-style-spacing);
  max-width: 50%;
}
#cke5-inline-demo header.ck-content{
  text-align: center;
}
#cke5-inline-demo header.ck-content h2 + h3{
  font-weight: 600;
}
#cke5-inline-demo .demo-row{
  display: flex;
  width: 100%;
  flex-direction: column;
}
@media (min-width: 640px){
  #cke5-inline-demo .demo-row{
    flex-direction: row;
  }
}
#cke5-inline-demo .demo-row .demo-row__half{
  width: 100%;
  padding-left: 0px;
  padding-right: 0px;
}
@media (min-width: 640px){
  #cke5-inline-demo .demo-row .demo-row__half{
    width: 50%;
  }
  #cke5-inline-demo .demo-row .demo-row__half:first-child:where([dir="ltr"], [dir="ltr"] *){
    padding-right: 0.5rem;
  }
  #cke5-inline-demo .demo-row .demo-row__half:last-child:where([dir="ltr"], [dir="ltr"] *){
    padding-left: 0.5rem;
  }
  #cke5-inline-demo .demo-row .demo-row__half:first-child:where([dir="rtl"], [dir="rtl"] *){
    padding-left: 0.5rem;
  }
  #cke5-inline-demo .demo-row .demo-row__half:last-child:where([dir="rtl"], [dir="rtl"] *){
    padding-right: 0.5rem;
  }
}
.dropzone{
  margin-bottom: 1.25rem;
  min-height: auto;
  cursor: pointer;
  border-radius: 0.5rem;
  border-width: 2px;
  border-style: dashed;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 1.25rem;
}
.dropzone:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(38 50 64 / var(--tw-bg-opacity));
}
.uppy-Dashboard--modal{
  z-index: 1030;
}
.uppy-Dashboard--modal .uppy-Dashboard-overlay{
  z-index: 1030;
}
.uppy-Dashboard--modal .uppy-Dashboard-inner{
  z-index: 1031;
}
.error-message{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.datepicker-picker{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.datepicker-picker:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.datepicker-controls .btn{
  background-color: transparent;
}
.datepicker-cell.disabled{
  color: rgb(19 25 32 / 0.5);
}
.datepicker-cell.disabled:is([data-pc-theme="dark"] *){
  color: rgb(191 191 191 / 0.5);
}
.datepicker-cell.focused:not(.selected),.datepicker-cell:not(.disabled):hover{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.datepicker-cell.highlighted:not(.selected):not(.range):not(.today):not(.disabled):hover,.datepicker-cell.highlighted:not(.selected):not(.range):not(.today).focused,.datepicker-cell.highlighted:not(.selected):not(.range):not(.today){
  background-color: rgba(0,0,0,0.1);
}
.datepicker-footer{
  background-color: transparent;
}
.ql-container.ql-snow,.ql-toolbar.ql-snow{
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
}
.ql-container.ql-snow:is([data-pc-theme="dark"] *),.ql-toolbar.ql-snow:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.ql-snow .ql-picker{
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.ql-snow .ql-picker:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.ql-snow .ql-stroke{
  stroke: #131920;
}
.ql-snow .ql-stroke:is([data-pc-theme="dark"] *){
  stroke: #bfbfbf;
}
.modal-content{
  margin: 0.5rem;
  display: flex;
  width: 100%;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 15px -3px rgb(0,0,0,0.1);
  --tw-shadow-colored: 0 0 15px -3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.modal-content:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.modal-content .modal-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  padding: 1.25rem;
}
.modal-content .modal-header:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.modal-content .modal-body{
  flex: 1 1 auto;
  padding: 1.25rem;
}
.modal-content .modal-footer{
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  padding: 1.25rem;
}
.modal-content .modal-footer:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.modal-dialog{
  position: relative;
  left: 0px;
  right: 0px;
  top: 0px;
  z-index: 1030;
  margin-left: auto;
  margin-right: auto;
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
  display: flex;
  max-width: 500px;
  --tw-translate-y: -5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  align-items: flex-start;
  opacity: 0;
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.modal-dialog.modal-lg{
  max-width: 800px;
}
.modal-dialog.modal-sm{
  max-width: 300px;
}
.modal-dialog.modal-fullscreen{
  margin: 0px;
  height: 100%;
  width: 100%;
  max-width: none;
}
.modal-dialog.modal-fullscreen .modal-content{
  height: 100%;
  border-radius: 0px;
}
.modal-dialog.modal-fullscreen .modal-content .modal-body{
  overflow-y: auto;
}
@media not all and (min-width: 1280px){
  .modal-dialog.modal-fullscreen-xl-down{
    margin: 0px;
    height: 100%;
    width: 100%;
    max-width: none;
  }
  .modal-dialog.modal-fullscreen-xl-down .modal-content{
    height: 100%;
    border-radius: 0px;
  }
  .modal-dialog.modal-fullscreen-xl-down .modal-content .modal-body{
    overflow-y: auto;
  }
}
@media not all and (min-width: 1024px){
  .modal-dialog.modal-fullscreen-lg-down{
    margin: 0px;
    height: 100%;
    width: 100%;
    max-width: none;
  }
  .modal-dialog.modal-fullscreen-lg-down .modal-content{
    height: 100%;
    border-radius: 0px;
  }
  .modal-dialog.modal-fullscreen-lg-down .modal-content .modal-body{
    overflow-y: auto;
  }
}
@media not all and (min-width: 768px){
  .modal-dialog.modal-fullscreen-md-down{
    margin: 0px;
    height: 100%;
    width: 100%;
    max-width: none;
  }
  .modal-dialog.modal-fullscreen-md-down .modal-content{
    height: 100%;
    border-radius: 0px;
  }
  .modal-dialog.modal-fullscreen-md-down .modal-content .modal-body{
    overflow-y: auto;
  }
}
@media not all and (min-width: 640px){
  .modal-dialog.modal-fullscreen-sm-down{
    margin: 0px;
    height: 100%;
    width: 100%;
    max-width: none;
  }
  .modal-dialog.modal-fullscreen-sm-down .modal-content{
    height: 100%;
    border-radius: 0px;
  }
  .modal-dialog.modal-fullscreen-sm-down .modal-content .modal-body{
    overflow-y: auto;
  }
}
.modal-dialog-centered{
  display: flex;
  min-height: calc(100% - 5rem);
  align-items: center;
}
.modal{
  position: fixed;
  left: 0px;
  right: 0px;
  top: 0px;
  z-index: 1030;
  display: none;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
.modal.show{
  display: block;
}
.modal.animate .modal-dialog{
  transform: none;
  opacity: 1;
}
.modal.anim-fade-in-scale .modal-dialog{
  --tw-translate-y: 0px;
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-fade-in-scale.animate .modal-dialog{
  transform: none;
  opacity: 1;
}
.modal.anim-slide-in-right .modal-dialog{
  --tw-translate-x: 25%;
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-property: all 0.3s cubic-bezier(0.25,0.5,0.5,0.9);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.modal.anim-slide-in-right.animate .modal-dialog{
  transform: none;
  opacity: 1;
}
.modal.anim-slide-in-bottom .modal-dialog{
  --tw-translate-y: 25%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-slide-in-bottom.animate .modal-dialog{
  transform: none;
  opacity: 1;
}
.modal.anim-newspaper .modal-dialog{
  --tw-translate-y: 0px;
  --tw-rotate: 720deg;
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-duration: 500ms;
}
.modal.anim-newspaper.animate .modal-dialog{
  transform: none;
  opacity: 1;
}
.modal.anim-fall{
  perspective: 1300px;
}
.modal.anim-fall .modal-dialog{
  transform: translateZ(600px) rotateX(20deg) translateY(0);
  opacity: 0;
  transition-duration: 500ms;
}
.modal.anim-fall.animate .modal-dialog{
  transform: translateZ(0px) rotateX(0deg) translateY(0);
  opacity: 1;
}
.modal.anim-side-fall{
  perspective: 1300px;
}
.modal.anim-side-fall .modal-dialog{
  transform-style: preserve-3d;
  transform: translate(30%) translateZ(600px) rotate(10deg);
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-side-fall.animate .modal-dialog{
  transform: translate(0%) translateZ(0) rotate(0deg);
  opacity: 1;
}
.modal.anim-sticky-up .modal-dialog.modal-dialog-centered{
  margin-top: 0px;
  align-items: flex-start;
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-sticky-up .modal-dialog.modal-dialog-centered .modal-content{
  margin-top: 0px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}
.modal.anim-sticky-up.animate .modal-dialog{
  opacity: 1;
}
.modal.anim-3d-flip-horizontal{
  perspective: 1300px;
}
.modal.anim-3d-flip-horizontal .modal-dialog{
  transform-style: preserve-3d;
  transform: rotateY(-70deg) translateY(0);
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-3d-flip-horizontal.animate .modal-dialog{
  transform: rotateY(0deg) translateY(0);
  opacity: 1;
}
.modal.anim-3d-flip-vertical{
  perspective: 1300px;
}
.modal.anim-3d-flip-vertical .modal-dialog{
  transform-style: preserve-3d;
  transform: rotateX(-70deg) translateY(0);
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-3d-flip-vertical.animate .modal-dialog{
  transform: rotateX(0deg) translateY(0);
  opacity: 1;
}
.modal.anim-3d-sign{
  perspective: 1300px;
}
.modal.anim-3d-sign .modal-dialog{
  transform-style: preserve-3d;
  transform: rotateX(-60deg) translateY(0);
  transform-origin: 50% 0;
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-3d-sign.animate .modal-dialog{
  transform: rotateX(0deg) translateY(0);
  opacity: 1;
}
.modal.anim-super-scaled{
  perspective: 1300px;
}
.modal.anim-super-scaled .modal-dialog{
  transform-style: preserve-3d;
  transform: scale(2) translateY(0);
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-super-scaled.animate .modal-dialog{
  transform: scale(1) translateY(0);
  opacity: 1;
}
.modal.anim-just-me{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.modal.anim-just-me:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.modal.anim-just-me .modal-dialog{
  transform: scale(0.8) translateY(0);
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-just-me .modal-dialog .modal-content{
  border-width: 0px;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.modal.anim-just-me.animate .modal-dialog{
  transform: scale(1) translateY(0);
  opacity: 1;
}
.modal.anim-3d-slit{
  perspective: 1300px;
}
.modal.anim-3d-slit .modal-dialog{
  transform-style: preserve-3d;
  transform: translateZ(-3000px) rotateY(90deg) translateY(0);
  opacity: 0;
  transition-duration: 300ms;
}
@keyframes slit{
  50%{
    transform: translateZ(-250px) rotateY(89deg);
    opacity: 1;
    animation-timing-function: ease-in;
  }
  100%{
    transform: translateZ(0) rotateY(0deg);
    opacity: 1;
  }
}
.modal.anim-3d-slit.animate .modal-dialog{
  animation: slit 0.7s forwards ease-out;
  opacity: 1;
}
.modal.anim-3d-rotate-bottom{
  perspective: 1300px;
}
.modal.anim-3d-rotate-bottom .modal-dialog{
  transform-style: preserve-3d;
  transform: translateY(100%) rotateX(90deg);
  transform-origin: 0 100%;
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-3d-rotate-bottom.animate .modal-dialog{
  transform: translateY(0%) rotateX(0deg);
  opacity: 1;
}
.modal.anim-3d-rotate-InLeft{
  perspective: 1300px;
}
.modal.anim-3d-rotate-InLeft .modal-dialog{
  transform-style: preserve-3d;
  transform: translateZ(100px) translateX(-30%) rotateY(90deg);
  transform-origin: 0 100%;
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-3d-rotate-InLeft.animate .modal-dialog{
  transform: translateZ(0px) translateX(0%) rotateY(0deg);
  opacity: 1;
}
.modal.anim-blur{
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.modal.anim-blur .modal-dialog{
  --tw-translate-y: 0px;
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-duration: 300ms;
}
.modal.anim-blur.animate .modal-dialog{
  transform: none;
  opacity: 1;
}
.modal-open{
  overflow: hidden;
}
.introjs-tooltip{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.introjs-tooltip:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.notifier-container{
  width: 400px;
  max-width: 98%;
}
.notifier{
  border-radius: 0.5rem;
  --tw-border-opacity: 1;
  border-color: rgb(91 107 121 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 1.25rem;
}
.notifier:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.notifier .notifier-title{
  margin-bottom: 0px;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  line-height: 1;
}
.notifier .notifier-body{
  margin-bottom: 0px;
  font-size: 0.875rem;
}
.notifier .notifier-img img{
  height: 2.5rem;
  width: 2.5rem;
}
.notifier .notifier-close:hover{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.notifier .notifier-close:focus{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.notifier .notifier-close:where([dir="ltr"], [dir="ltr"] *){
  right: 0.25rem;
}
.notifier .notifier-close:where([dir="rtl"], [dir="rtl"] *){
  left: 0.25rem;
  right: auto;
}
.notifier.info{
  --tw-border-opacity: 1;
  border-color: rgb(62 201 214 / var(--tw-border-opacity));
}
.notifier.success{
  --tw-border-opacity: 1;
  border-color: rgb(44 168 127 / var(--tw-border-opacity));
}
.notifier.danger{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}
.notifier.warning{
  --tw-border-opacity: 1;
  border-color: rgb(229 138 0 / var(--tw-border-opacity));
}
.offcanvas{
  visibility: hidden;
  position: fixed;
  z-index: 1028;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 15px -3px rgb(0,0,0,0.1);
  --tw-shadow-colored: 0 0 15px -3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.offcanvas:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.offcanvas:not(.show){
  z-index: 1;
}
.offcanvas .offcanvas-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem;
}
.offcanvas .offcanvas-body{
  padding: 1.25rem;
}
.offcanvas .offcanvas-body::-webkit-scrollbar{
  width: 0.375rem;
  opacity: 0;
}
.offcanvas .offcanvas-body::-webkit-scrollbar:hover{
  opacity: 1;
}
.offcanvas .offcanvas-body::-webkit-scrollbar-track{
  background-color: transparent;
}
.offcanvas .offcanvas-body::-webkit-scrollbar-thumb{
  --tw-bg-opacity: 1;
  background-color: rgb(225 229 232 / var(--tw-bg-opacity));
}
.offcanvas .offcanvas-body::-webkit-scrollbar-thumb:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(190 198 206 / var(--tw-bg-opacity));
}
.offcanvas.offcanvas-start{
  top: 0px;
  bottom: 0px;
  left: -360px;
  width: 360px;
  max-width: 100%;
}
.offcanvas.offcanvas-start.show{
  left: 0px;
}
.offcanvas.offcanvas-top{
  left: 0px;
  right: 0px;
  top: -320px;
  height: 320px;
}
.offcanvas.offcanvas-top.show{
  top: 0px;
}
.offcanvas.offcanvas-end{
  top: 0px;
  bottom: 0px;
  right: -360px;
  width: 360px;
  max-width: 100%;
}
.offcanvas.offcanvas-end.show{
  right: 0px;
}
.offcanvas.offcanvas-bottom{
  left: 0px;
  right: 0px;
  bottom: -320px;
  height: 320px;
}
.offcanvas.offcanvas-bottom.show{
  bottom: 0px;
}
.offcanvas.show{
  visibility: visible;
}
.slider.slider-horizontal .slider-handle.triangle, .slider.slider-horizontal .slider-tick.triangle{
  border-bottom-color: rgb(var(--colors-primary-500));
}
.slider.slider-vertical .slider-handle.triangle, .slider.slider-vertical .slider-tick.triangle{
  border-left-color: rgb(var(--colors-primary-500));
  border-right-color: rgb(var(--colors-primary-500));
}
.slider.slider-disabled .slider-handle{
  background-image: linear-gradient(to bottom, #dfdfdf 0, #bebebe 100%);
  background-repeat: repeat-x;
}
.slider.slider-disabled .slider-track{
  background-image: linear-gradient(to bottom, #f8f9fa 0, #f8f9fa 100%);
  background-repeat: repeat-x;
}
.slider.slider-disabled .slider-track:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.slider-track{
  background-image: linear-gradient(to bottom, #f8f9fa 0, #f8f9fa 100%);
  background-repeat: repeat-x;
}
.slider-track:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.slider-selection{
  background-color: rgb(var(--colors-primary-300));
  background-image: none;
}
.slider-selection.tick-slider-selection{
  background-color: rgb(var(--colors-primary-200));
  background-image: none;
}
.slider-handle{
  background-color: rgb(var(--colors-primary-500));
  background-image: none;
}
.slider-tick{
  background-image: linear-gradient(to bottom, #f9f9f9 0, #f5f5f5 100%);
  background-repeat: repeat-x;
}
.slider-tick.in-selection{
  background-color: rgb(var(--colors-primary-200));
  background-image: none;
}
.slider .tooltip{
  position: absolute;
  z-index: 1024;
  max-width: 12rem;
}
.slider .tooltip .tooltip-inner{
  position: relative;
  left: -50%;
  white-space: nowrap;
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.slider .tooltip:not(.show){
  display: none;
}
#ex7-enabled{
  position: relative;
  top: 0.25rem;
  height: 18px;
  width: 18px;
}
#RGB{
  height: 0.625rem;
  --tw-bg-opacity: 1;
  background-color: rgb(128 128 128 / var(--tw-bg-opacity));
}
#RC .slider-selection{
  --tw-bg-opacity: 1;
  background-color: rgb(255 130 130 / var(--tw-bg-opacity));
}
#RC .slider-handle{
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 0 / var(--tw-bg-opacity));
}
#GC .slider-selection{
  --tw-bg-opacity: 1;
  background-color: rgb(71 143 59 / var(--tw-bg-opacity));
}
#GC .slider-handle{
  --tw-bg-opacity: 1;
  background-color: rgb(0 128 0 / var(--tw-bg-opacity));
}
#BC .slider-selection{
  --tw-bg-opacity: 1;
  background-color: rgb(130 131 255 / var(--tw-bg-opacity));
}
#BC .slider-handle{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(0 0 255 / var(--tw-border-opacity));
}
#R,#G,#B{
  width: 300;
}
.slider-handle.custom{
  background-color: transparent;
}
.slider-handle.custom::before{
  font-size: 28px;
  line-height: 15px;
  --tw-text-opacity: 1;
  color: rgb(114 98 4 / var(--tw-text-opacity));
  --tw-content: '\2605';
  content: var(--tw-content);
}
#slider12a .slider-track-high, #slider12c .slider-track-high{
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
}
#slider12b .slider-track-low{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
#slider12c .slider-track-low{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
#slider12c .slider-selection{
  --tw-bg-opacity: 1;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity));
}
#slider22 .slider-rangeHighlight{
  --tw-bg-opacity: 1;
  background-color: rgb(247 6 22 / var(--tw-bg-opacity));
}
#slider22 .slider-rangeHighlight.category1{
  --tw-bg-opacity: 1;
  background-color: rgb(153 204 0 / var(--tw-bg-opacity));
}
.simplebar-scrollbar{
  position: absolute;
  width: 0.375rem;
}
.simplebar-scrollbar::before{
  position: absolute;
  left: 0px;
  right: 0px;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
  content: var(--tw-content);
  opacity: 0;
}
.simplebar-scrollbar:where([dir="ltr"], [dir="ltr"] *){
  right: 1px;
}
.simplebar-scrollbar:where([dir="rtl"], [dir="rtl"] *){
  left: 1px;
}
.simplebar-scrollbar{
  min-height: 10px;
}
.simplebar-scrollbar:before{
  content: ' ';
  transition: opacity 0.2s linear;
}
.simplebar-scrollbar.simplebar-visible:before{
  opacity: 0.5;
  transition: opacity 0s linear;
}
.tns-controls{
  margin-bottom: 0.625rem;
  text-align: center;
}
.tns-controls [aria-controls]{
  margin: 0 5px;
  height: 2.5em;
  border-radius: 0.25rem;
  border-width: 0px;
  background-color: rgb(var(--colors-primary-500));
  padding: 0 1em;
  font-size: 0.875rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
[data-action]{
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
  margin-left: auto;
  margin-right: auto;
  display: block;
  min-width: 3em;
  border-width: 0px;
  background-color: transparent;
  text-align: center;
  font-size: 17px;
}
.tns-controls [disabled]{
  cursor: not-allowed;
  --tw-bg-opacity: 1;
  background-color: rgb(179 179 179 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(153 153 153 / var(--tw-text-opacity));
}
.tns-nav{
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
  text-align: center;
}
.tns-nav > [aria-controls]{
  margin-top: 0px;
  margin-bottom: 0px;
  margin-left: 5px;
  margin-right: 5px;
  height: 9px;
  width: 9px;
  border-radius: 9999px;
  border-width: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(221 221 221 / var(--tw-bg-opacity));
  padding: 0px;
}
.tns-nav > .tns-nav-active{
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity));
}
.thumbnails{
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
  text-align: center;
}
.thumbnails li{
  display: inline-block;
  cursor: pointer;
  opacity: 0.5;
}
.thumbnails li.tns-nav-active{
  background-color: transparent;
  opacity: 1;
}
.thumbnails img{
  height: auto;
  width: 46px;
}
.customize-tools{
  position: relative;
}
.customize-tools .controls{
  display: none;
}
@media (min-width: 768px){
  .customize-tools .controls{
    display: block;
  }
}
.controls{
  text-align: center;
}
.controls li{
  position: absolute;
  top: 50%;
  margin-top: -30px;
  display: block;
  height: 60px;
  cursor: pointer;
  padding-top: 0px;
  padding-bottom: 0px;
  padding-left: 15px;
  padding-right: 15px;
  font-size: 1.875rem;
  line-height: 2.25rem;
  line-height: 60px;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.controls li:hover{
  background-color: rgba(0,0,0,.1);
}
.controls .prev{
  left: 0px;
}
.controls .next{
  right: 0px;
}
.swal2-popup,div:where(.swal2-container) .swal2-radio, div:where(.swal2-container) .swal2-checkbox, div:where(.swal2-container) .swal2-range{
  border-radius: 0.75rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.swal2-popup:is([data-pc-theme="dark"] *),div:where(.swal2-container) .swal2-radio:is([data-pc-theme="dark"] *), div:where(.swal2-container) .swal2-checkbox:is([data-pc-theme="dark"] *), div:where(.swal2-container) .swal2-range:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
button.swal2-styled{
  display: inline-block;
  border-radius: 20px !important;
  border-width: 1px;
  border-color: transparent !important;
  background-color: transparent;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-size: 0.875rem !important;
  font-weight: 500;
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
button.swal2-styled:focus{
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.swal2-icon.swal2-error{
  border-color: rgb(220 38 38 / 0.15) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(220 38 38 / var(--tw-text-opacity)) !important;
}
.swal2-icon.swal2-error [class^="swal2-x-mark-line"]{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity)) !important;
}
.swal2-icon.swal2-warning{
  border-color: rgb(229 138 0 / 0.15) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(229 138 0 / var(--tw-text-opacity)) !important;
}
.swal2-icon.swal2-info{
  border-color: rgb(62 201 214 / 0.15) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(62 201 214 / var(--tw-text-opacity)) !important;
}
.swal2-icon.swal2-question{
  border-color: rgb(168 85 247 / 0.15) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(168 85 247 / var(--tw-text-opacity)) !important;
}
.swal2-icon.swal2-success{
  border-color: rgb(44 168 127 / 0.15) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(44 168 127 / var(--tw-text-opacity)) !important;
}
.swal2-icon.swal2-success .swal2-success-ring{
  border-color: rgb(44 168 127 / 0.3) !important;
}
.swal2-icon.swal2-success [class^="swal2-success-line"]{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity)) !important;
}
.swal2-actions{
  gap: 1rem;
}
.swal2-styled.swal2-confirm{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.swal2-styled.swal2-confirm:hover{
  background-color: rgb(var(--colors-primary-600));
}
.swal2-styled.swal2-confirm:focus{
  background-color: rgb(var(--colors-primary-600));
}
.swal2-styled.swal2-confirm:active{
  background-color: rgb(var(--colors-primary-700));
}
.swal2-styled.swal2-deny{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.swal2-styled.swal2-deny:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(216 34 34 / var(--tw-bg-opacity));
}
.swal2-styled.swal2-deny:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(216 34 34 / var(--tw-bg-opacity));
}
.swal2-styled.swal2-deny:active{
  --tw-bg-opacity: 1;
  background-color: rgb(211 28 28 / var(--tw-bg-opacity));
}
.swal2-styled.swal2-cancel{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.swal2-styled.swal2-cancel:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
}
.swal2-styled.swal2-cancel:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
}
.swal2-styled.swal2-cancel:active{
  --tw-bg-opacity: 1;
  background-color: rgb(56 66 74 / var(--tw-bg-opacity));
}
.table-responsive{
  overflow-x: auto;
}
.table{
  margin-bottom: 1rem;
  width: 100%;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  vertical-align: top;
}
.table:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.table > :not(caption) > * > *{
  white-space: nowrap;
  padding: .7rem .75rem;
}
.table thead{
  background-color: rgba(248,249,250,0.5);
  vertical-align: bottom;
}
.table thead:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(48 63 80 / var(--tw-bg-opacity));
}
.table thead th{
  border-top-width: 1px;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-top-color: rgb(231 234 238 / var(--tw-border-opacity));
  border-bottom-color: rgb(231 234 238 / var(--tw-border-opacity));
  padding: .9rem .75rem;
  vertical-align: middle;
  font-size: 13px;
  text-transform: uppercase;
}
.table thead th:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(48 63 80 / var(--tw-border-opacity));
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.table thead th:where([dir="ltr"], [dir="ltr"] *){
  text-align: left;
}
.table thead th:where([dir="rtl"], [dir="rtl"] *){
  text-align: right;
}
.table:not(:last-child)>:last-child>*{
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-top-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.table:not(:last-child)>:last-child>*:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.table td, .table th{
  border-bottom-width: 0px;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-top-color: rgb(231 234 238 / var(--tw-border-opacity));
  vertical-align: middle !important;
}
.table td:is([data-pc-theme="dark"] *), .table th:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.table.table-hover tbody tr:hover{
  background-color: rgba(0,0,0,.04) !important;
}
.table.table-striped tbody tr:nth-child(odd){
  background-color: rgb(155 168 180 / 0.1);
}
.table.table-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.table.table-dark tbody tr td{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.table.table-xl th, .table.table-xl td{
  padding: 1.25rem .8rem;
}
.table.table-lg th, .table.table-lg td{
  padding: .9rem .8rem;
}
.table.table-sm th, .table.table-sm td{
  padding: .6rem .8rem;
}
.table.table-xs th, .table.table-xs td{
  padding: .4rem .8rem;
}
.table.table-bordered th, .table.table-bordered td{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.table.table-bordered th:is([data-pc-theme="dark"] *), .table.table-bordered td:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.table.table-borderless th, .table.table-borderless td{
  border-width: 0px !important;
}
.datatable-table > thead > tr > th:where([dir="ltr"], [dir="ltr"] *){
  text-align: left;
}
.datatable-table > thead > tr > th:where([dir="rtl"], [dir="rtl"] *){
  text-align: right;
}
.datatable-top{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.datatable-top::after{
  content: var(--tw-content);
  display: none;
}
@media not all and (min-width: 640px){
  .datatable-top{
    flex-direction: column;
    align-items: flex-start;
  }
}
.datatable-sorter:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 1rem;
}
.datatable-sorter:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 1rem;
}
.datatable-sorter:where([dir="ltr"], [dir="ltr"] *):before, .datatable-sorter:where([dir="ltr"], [dir="ltr"] *):after{
  right: 0.25rem;
}
.datatable-sorter:where([dir="rtl"], [dir="rtl"] *):before, .datatable-sorter:where([dir="rtl"], [dir="rtl"] *):after{
  left: 0.25rem;
  right: auto;
}
.datatable-sorter:before{
  --tw-border-opacity: 1;
  border-top-color: rgb(19 25 32 / var(--tw-border-opacity));
}
.datatable-sorter:is([data-pc-theme="dark"] *):before{
  --tw-border-opacity: 1;
  border-top-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.datatable-sorter:after{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(19 25 32 / var(--tw-border-opacity));
}
.datatable-sorter:is([data-pc-theme="dark"] *):after{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.datatable-wrapper .datatable-container{
  overflow-x: auto;
  border-bottom-width: 0px !important;
}
.datatable-dropdown{
  margin-bottom: 0.25rem;
}
.datatable-dropdown label{
  display: flex;
  width: 230px;
  align-items: center;
  white-space: nowrap;
}
.datatable-dropdown label select:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px;
  margin-right: 0.5rem;
}
.datatable-dropdown label select:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px;
  margin-left: 0.5rem;
}
.datatable-pagination a:focus, .datatable-pagination a:hover, .datatable-pagination button:focus, .datatable-pagination button:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.datatable-pagination a:focus:is([data-pc-theme="dark"] *), .datatable-pagination a:hover:is([data-pc-theme="dark"] *), .datatable-pagination button:focus:is([data-pc-theme="dark"] *), .datatable-pagination button:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
  color: rgba(255, 255, 255, 0.8);
}
.datatable-pagination .datatable-active a,.datatable-pagination .datatable-active button{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.datatable-pagination .datatable-active a:is([data-pc-theme="dark"] *),.datatable-pagination .datatable-active button:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
  color: rgba(255, 255, 255, 0.8);
}
.datatable-pagination .datatable-active a:focus, .datatable-pagination .datatable-active a:hover, .datatable-pagination .datatable-active button:focus, .datatable-pagination .datatable-active button:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.datatable-pagination .datatable-active a:focus:is([data-pc-theme="dark"] *), .datatable-pagination .datatable-active a:hover:is([data-pc-theme="dark"] *), .datatable-pagination .datatable-active button:focus:is([data-pc-theme="dark"] *), .datatable-pagination .datatable-active button:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
  color: rgba(255, 255, 255, 0.8);
}
.dt-container>div.row.justify-content-between{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
@media not all and (min-width: 768px){
  .dt-container>div.row.justify-content-between{
    text-align: center;
  }
  .dt-container>div.row.justify-content-between > *{
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 0.5rem;
  }
}
@media (min-width: 768px){
  .dt-container>div.row.justify-content-between{
    flex-direction: row;
  }
}
.dt-container>div.row.mt-2{
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}
div.dt-container div.dt-length select{
  width: 5rem;
}
div.dt-container div.dt-search input:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0.5rem;
}
div.dt-container div.dt-search input:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0.5rem;
}
.dt-scroll-headInner:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 17px !important;
}
.dt-scroll-headInner:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 17px !important;
  padding-right: 0px !important;
}
table.\!dataTable thead>tr> th:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 2rem;
  text-align: left;
}
table.\!dataTable thead>tr> th:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 2rem !important;
  padding-right: .75rem !important;
  text-align: right;
}
table.dataTable thead>tr> th:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 2rem;
  text-align: left;
}
table.dataTable thead>tr> th:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 2rem !important;
  padding-right: .75rem !important;
  text-align: right;
}
table.\!dataTable thead>tr> th .dt-column-order:where([dir="ltr"], [dir="ltr"] *){
  right: 0.75rem !important;
}
table.\!dataTable thead>tr> th .dt-column-order:where([dir="rtl"], [dir="rtl"] *){
  left: 0.75rem !important;
  right: auto !important;
}
table.dataTable thead>tr> th .dt-column-order:where([dir="ltr"], [dir="ltr"] *){
  right: 0.75rem !important;
}
table.dataTable thead>tr> th .dt-column-order:where([dir="rtl"], [dir="rtl"] *){
  left: 0.75rem !important;
  right: auto !important;
}
.dt-search{
  margin-bottom: 0.5rem;
}
.dt-buttons{
  margin-bottom: 0.25rem;
}
.dt-buttons~.dt-search{
  margin-bottom: 1rem;
}
div.dt-container div.dt-paging ul.pagination, .dt-paging .pagination{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  display: flex;
}
div.dt-container div.dt-paging ul.pagination > * > *, .dt-paging .pagination > * > *{
  display: inline-block;
}
div.dt-container div.dt-paging ul.pagination > *, .dt-paging .pagination > *{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
div.dt-container div.dt-paging ul.pagination > * > *, .dt-paging .pagination > * > *{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
div.dt-container div.dt-paging ul.pagination > *:first-child, .dt-paging .pagination > *:first-child{
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
div.dt-container div.dt-paging ul.pagination > *:last-child, .dt-paging .pagination > *:last-child{
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
div.dt-container div.dt-paging ul.pagination > *:hover, .dt-paging .pagination > *:hover{
  background-color: rgb(155 168 180 / 0.1);
}
div.dt-container div.dt-paging ul.pagination:is([data-pc-theme="dark"] *) > *, .dt-paging .pagination:is([data-pc-theme="dark"] *) > *{
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
div.dt-container div.dt-paging ul.pagination .active>.page-link, div.dt-container div.dt-paging ul.pagination .page-link.active, .dt-paging .pagination .active>.page-link, .dt-paging .pagination .page-link.active{
  border-color: rgb(var(--colors-primary-500));
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
div.dt-scroll-body{
  border-bottom-width: 0px;
}
.dtfh-floatingparent.dtfh-floatingparent-head{
  top: 74px !important;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.dtfh-floatingparent.dtfh-floatingparent-head:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.dtfh-floatingparent.dtfh-floatingparent-foot{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.dtfh-floatingparent.dtfh-floatingparent-foot:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
table.\!dataTable tbody tr >.dtfc-fixed-start, table.\!dataTable tbody tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.\!dataTable tbody tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *), table.\!dataTable tbody tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
table.dataTable tbody tr >.dtfc-fixed-start, table.dataTable tbody tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.dataTable tbody tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *), table.dataTable tbody tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
table.\!dataTable tbody tr >.dtfc-fixed-start, table.\!dataTable tbody tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.\!dataTable tbody tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *), table.\!dataTable tbody tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
table.\!dataTable thead tr >.dtfc-fixed-start,table.\!dataTable thead tr >.dtfc-fixed-end,table.\!dataTable tfoot tr >.dtfc-fixed-start,table.\!dataTable tfoot tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.\!dataTable thead tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.\!dataTable thead tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *),table.\!dataTable tfoot tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.\!dataTable tfoot tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
table.dataTable thead tr >.dtfc-fixed-start,table.dataTable thead tr >.dtfc-fixed-end,table.dataTable tfoot tr >.dtfc-fixed-start,table.dataTable tfoot tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.dataTable thead tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.dataTable thead tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *),table.dataTable tfoot tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.dataTable tfoot tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
table.\!dataTable thead tr >.dtfc-fixed-start,table.\!dataTable thead tr >.dtfc-fixed-end,table.\!dataTable tfoot tr >.dtfc-fixed-start,table.\!dataTable tfoot tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.\!dataTable thead tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.\!dataTable thead tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *),table.\!dataTable tfoot tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.\!dataTable tfoot tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
table.\!dataTable thead tr >.dtfc-fixed-start,table.\!dataTable thead tr >.dtfc-fixed-end,table.\!dataTable tfoot tr >.dtfc-fixed-start,table.\!dataTable tfoot tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.\!dataTable thead tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.\!dataTable thead tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *),table.\!dataTable tfoot tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.\!dataTable tfoot tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
table.\!dataTable thead tr >.dtfc-fixed-start,table.\!dataTable thead tr >.dtfc-fixed-end,table.\!dataTable tfoot tr >.dtfc-fixed-start,table.\!dataTable tfoot tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.\!dataTable thead tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.\!dataTable thead tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *),table.\!dataTable tfoot tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.\!dataTable tfoot tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.table-card .card-body, .table-body.card-body {
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 0px;
}
.table-card .card-body .datatable-top, .table-card .card-body .datatable-bottom, .table-body.card-body .datatable-top, .table-body.card-body .datatable-bottom{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
@media (min-width: 640px){
  .table-card .card-body .datatable-top, .table-card .card-body .datatable-bottom, .table-body.card-body .datatable-top, .table-body.card-body .datatable-bottom{
    padding-left: 25px;
    padding-right: 25px;
  }
}
.table-card .card-body .table > thead > tr > th, .table-body.card-body .table > thead > tr > th{
  border-top-width: 0px;
}
.table-card .card-body .table tr td:first-child:where([dir="ltr"], [dir="ltr"] *), .table-card .card-body .table tr th:first-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr td:first-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr th:first-child:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 1.25rem;
}
.table-card .card-body .table tr td:last-child:where([dir="ltr"], [dir="ltr"] *), .table-card .card-body .table tr th:last-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr td:last-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr th:last-child:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 1.25rem;
}
@media (min-width: 640px){
  .table-card .card-body .table tr td:first-child:where([dir="ltr"], [dir="ltr"] *), .table-card .card-body .table tr th:first-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr td:first-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr th:first-child:where([dir="ltr"], [dir="ltr"] *){
    padding-left: 25px;
  }
  .table-card .card-body .table tr td:last-child:where([dir="ltr"], [dir="ltr"] *), .table-card .card-body .table tr th:last-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr td:last-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr th:last-child:where([dir="ltr"], [dir="ltr"] *){
    padding-right: 25px;
  }
}
.table-card .card-body .table tr td:first-child:where([dir="rtl"], [dir="rtl"] *), .table-card .card-body .table tr th:first-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr td:first-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr th:first-child:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 1.25rem;
}
.table-card .card-body .table tr td:last-child:where([dir="rtl"], [dir="rtl"] *), .table-card .card-body .table tr th:last-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr td:last-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr th:last-child:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 1.25rem;
}
@media (min-width: 640px){
  .table-card .card-body .table tr td:first-child:where([dir="rtl"], [dir="rtl"] *), .table-card .card-body .table tr th:first-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr td:first-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr th:first-child:where([dir="rtl"], [dir="rtl"] *){
    padding-right: 25px;
  }
  .table-card .card-body .table tr td:last-child:where([dir="rtl"], [dir="rtl"] *), .table-card .card-body .table tr th:last-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr td:last-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr th:last-child:where([dir="rtl"], [dir="rtl"] *){
    padding-left: 25px;
  }
}
.fc td, .fc th{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.fc td:is([data-pc-theme="dark"] *), .fc th:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.fc .fc-toolbar{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media not all and (min-width: 640px){
  .fc .fc-toolbar{
    flex-direction: column;
    gap: 0.5rem;
  }
}
.fc .fc-toolbar h2{
  font-size: 16px;
  text-transform: uppercase;
  line-height: 30px;
}
@media not all and (min-width: 768px){
  .fc .fc-toolbar .fc-left, .fc .fc-toolbar .fc-right, .fc .fc-toolbar .fc-center{
    float: none;
    margin-top: 0.625rem;
    margin-bottom: 0.625rem;
    margin-left: 0px;
    margin-right: 0px;
    display: block;
    text-align: center;
  }
  .fc .fc-toolbar > * > *{
    float: none;
  }
}
.fc .fc-toolbar .fc-today-button{
  border-radius: 9999px;
  border-color: rgb(var(--colors-primary-500));
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
@media not all and (min-width: 768px){
  .fc .fc-toolbar .fc-today-button{
    display: none;
  }
}
.fc .fc-toolbar .fc-button{
  text-transform: capitalize;
}
.fc .fc-daygrid-day-top{
  flex-direction: column;
  padding: 14px;
}
.fc .fc-col-header-cell{
  border-width: 0px;
  background-color: transparent;
}
.fc .fc-col-header-cell-cushion{
  display: block;
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.fc .fc-scrollgrid-section .fc-scroller-harness{
  padding: 0px;
}
.fc .fc-daygrid-day-number{
  margin: 0.125rem;
  display: flex;
  height: 1.5rem;
  width: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 0px;
  font-size: 0.75rem;
}
.fc .fc-daygrid-day-number:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.fc .fc-daygrid-day.fc-day-today{
  background-image: url("../images/application/img-cal-bg.jpg");
  background-size: cover;
}
.fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-number{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.fc .fc-timegrid-col.fc-day-today{
  background-color: rgb(var(--colors-primary-500) / 0.1);
}
.fc .fc-col-header, .fc .fc-daygrid-body, .fc .fc-scrollgrid-sync-table{
  width: 100% !important;
}
.fc .fc-scrollgrid-section-sticky > *{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.fc .fc-scrollgrid-section-sticky > *:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.fc .fc-scrollgrid-section > *{
  border-left-width: 1px;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.fc .fc-scrollgrid-section-liquid > td{
  border-top-width: 0px;
}
.fc a[data-navlink]{
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}
.fc a[data-navlink]:hover{
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}
.fc-theme-bootstrap a:not([href]){
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.fc-theme-bootstrap a:not([href]):is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.fc-theme-standard .fc-scrollgrid{
  border-width: 0px;
}
.fc-h-event .fc-event-main{
  color: rgb(var(--colors-primary));
}
.fc-event-title, .fc-sticky{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600;
}
.fc-daygrid-event-dot{
  display: none;
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 255 255 / var(--tw-border-opacity)) !important;
}
.fc-event-time{
  display: none;
}
.fc-event .fc-content, .fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark .fc-content{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.fc-prev-button.fc-button, .fc-next-button.fc-button{
  position: relative;
  display: inline-block;
  height: 34px;
  width: 34px;
  align-items: center;
  justify-content: center;
  border-radius: 9999px !important;
  padding: 0px !important;
  font-size: 0.875rem;
}
@media not all and (min-width: 768px){
  .fc-toolbar-chunk{
    flex-direction: column;
    gap: 1rem;
  }
}
.fc-toolbar-chunk .d-inline-flex .fc-button{
  border-radius: 9999px !important;
  border-width: 0px;
  background-color: rgb(var(--colors-primary-500) / 0.15);
  color: rgb(var(--colors-primary));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.fc-toolbar-chunk .d-inline-flex .fc-button:not(:first-child){
  margin-left: 5px;
}
.fc-toolbar-chunk .d-inline-flex .fc-button:focus,.fc-toolbar-chunk .d-inline-flex .fc-button:hover,.fc-toolbar-chunk .d-inline-flex .fc-button.active{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.fc-toolbar-chunk .fc-button-primary{
  border-color: rgb(var(--colors-primary-500));
  background-color: rgb(var(--colors-primary-500) / 0.1);
  padding: 9px 16px;
  color: rgb(var(--colors-primary-500));
}
.fc-toolbar-chunk .fc-button-primary:focus{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.fc-toolbar-chunk .fc-button-primary:focus,.fc-toolbar-chunk .fc-button-primary:hover{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.fc-toolbar-chunk .fc-button-primary:not(:disabled).fc-button-active,.fc-toolbar-chunk .fc-button-primary:not(:disabled):active{
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.fc-toolbar-chunk .fc-button-primary:not(:disabled).fc-button-active:focus,.fc-toolbar-chunk .fc-button-primary:not(:disabled):active:focus{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.fc-toolbar-chunk .fc-today-button{
  border-radius: 9999px;
  border-color: rgb(var(--colors-primary-500));
  background-color: rgb(var(--colors-primary-500));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.fc-daygrid-event-harness .fc-daygrid-event, .fc-timegrid-event-harness .fc-daygrid-event{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.fc-timegrid-slots table tr, .fc-list-table{
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.fc-timegrid-slots table tr:is([data-pc-theme="dark"] *), .fc-list-table:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.fc-event{
  margin: 5px 7px;
  cursor: move;
  border-radius: 9999px;
  border-width: 1px;
  border-color: rgb(var(--colors-primary-500));
  background-color: rgb(var(--colors-primary-500) / 0.1);
  padding: 5px;
  text-align: center;
  font-size: 0.875rem;
  color: rgb(var(--colors-primary));
}
.fc-event.event-primary{
  border-color: rgb(var(--colors-primary-500));
  background-color: rgb(var(--colors-primary-500) / 0.1);
  color: rgb(var(--colors-primary-500));
}
.fc-event.event-primary.fc-h-event .fc-event-main{
  color: rgb(var(--colors-primary-500));
}
.fc-event.event-secondary{
  --tw-border-opacity: 1;
  border-color: rgb(91 107 121 / var(--tw-border-opacity));
  background-color: rgb(91 107 121 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.fc-event.event-secondary.fc-h-event .fc-event-main{
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.fc-event.event-success{
  --tw-border-opacity: 1;
  border-color: rgb(44 168 127 / var(--tw-border-opacity));
  background-color: rgb(44 168 127 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(44 168 127 / var(--tw-text-opacity));
}
.fc-event.event-success.fc-h-event .fc-event-main{
  --tw-text-opacity: 1;
  color: rgb(44 168 127 / var(--tw-text-opacity));
}
.fc-event.event-danger{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
  background-color: rgb(220 38 38 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.fc-event.event-danger.fc-h-event .fc-event-main{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.fc-event.event-warning{
  --tw-border-opacity: 1;
  border-color: rgb(229 138 0 / var(--tw-border-opacity));
  background-color: rgb(229 138 0 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(229 138 0 / var(--tw-text-opacity));
}
.fc-event.event-warning.fc-h-event .fc-event-main{
  --tw-text-opacity: 1;
  color: rgb(229 138 0 / var(--tw-text-opacity));
}
.fc-event.event-info{
  --tw-border-opacity: 1;
  border-color: rgb(62 201 214 / var(--tw-border-opacity));
  background-color: rgb(62 201 214 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(62 201 214 / var(--tw-text-opacity));
}
.fc-event.event-info.fc-h-event .fc-event-main{
  --tw-text-opacity: 1;
  color: rgb(62 201 214 / var(--tw-text-opacity));
}
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-dark .fc-event-main,.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-dark .fc-event-title{
  --tw-text-opacity: 1 !important;
  color: rgb(33 37 41 / var(--tw-text-opacity)) !important;
}
.fc-v-event .fc-event-main{
  color: inherit;
}
.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none{
  pointer-events: none;
}
.visible{
  visibility: visible;
}
.invisible{
  visibility: hidden;
}
.collapse{
  visibility: collapse;
}
.static{
  position: static;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.inset-0{
  inset: 0px;
}
.inset-\[4px\]{
  inset: 4px;
}
.inset-x-0{
  left: 0px;
  right: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.\!left-0{
  left: 0px !important;
}
.\!left-auto{
  left: auto !important;
}
.\!right-auto{
  right: auto !important;
}
.\!top-header-height{
  top: 74px !important;
}
.-bottom-5{
  bottom: -1.25rem;
}
.-left-2\.5{
  left: -0.625rem;
}
.-left-2\/4{
  left: -50%;
}
.-left-4{
  left: -1rem;
}
.-left-\[23px\]{
  left: -23px;
}
.-left-sidebar-collapsed-active-width{
  left: -300px;
}
.-left-sidebar-tab-navbar-width{
  left: -320px;
}
.-top-16{
  top: -4rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-1{
  bottom: 0.25rem;
}
.bottom-\[-15px\]{
  bottom: -15px;
}
.bottom-\[-320px\]{
  bottom: -320px;
}
.bottom-\[50px\]{
  bottom: 50px;
}
.left-0{
  left: 0px;
}
.left-2{
  left: 0.5rem;
}
.left-2\.5{
  left: 0.625rem;
}
.left-2\/4{
  left: 50%;
}
.left-3{
  left: 0.75rem;
}
.left-5{
  left: 1.25rem;
}
.left-\[-300px\]{
  left: -300px;
}
.left-\[-360px\]{
  left: -360px;
}
.left-\[15px\]{
  left: 15px;
}
.left-\[200px\]{
  left: 200px;
}
.left-\[3px\]{
  left: 3px;
}
.left-\[92px\]{
  left: 92px;
}
.left-\[94px\]{
  left: 94px;
}
.left-auto{
  left: auto;
}
.left-full{
  left: 100%;
}
.left-sidebar-collapsed-active-width{
  left: 300px;
}
.left-sidebar-collapsed-width{
  left: 100px;
}
.left-sidebar-tab-navbar-width{
  left: 320px;
}
.right-0{
  right: 0px;
}
.right-3{
  right: 0.75rem;
}
.right-\[-360px\]{
  right: -360px;
}
.right-\[-474px\]{
  right: -474px;
}
.right-\[30px\]{
  right: 30px;
}
.right-\[calc\(100\%_\+_32px\)\]{
  right: calc(100% + 32px);
}
.right-auto{
  right: auto;
}
.right-full{
  right: 100%;
}
.top-0{
  top: 0px;
}
.top-1{
  top: 0.25rem;
}
.top-10{
  top: 2.5rem;
}
.top-2{
  top: 0.5rem;
}
.top-2\/4{
  top: 50%;
}
.top-4{
  top: 1rem;
}
.top-5{
  top: 1.25rem;
}
.top-6{
  top: 1.5rem;
}
.top-\[-15px\]{
  top: -15px;
}
.top-\[-320px\]{
  top: -320px;
}
.top-\[-90px\]{
  top: -90px;
}
.top-\[100px\]{
  top: 100px;
}
.top-\[14px\]{
  top: 14px;
}
.top-\[26px\]{
  top: 26px;
}
.top-auto{
  top: auto;
}
.top-full{
  top: 100%;
}
.top-header-height{
  top: 74px;
}
.top-topbar-height{
  top: 60px;
}
.\!z-\[1031\]{
  z-index: 1031 !important;
}
.-z-10{
  z-index: -10;
}
.z-0{
  z-index: 0;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-50{
  z-index: 50;
}
.z-\[1002\]{
  z-index: 1002;
}
.z-\[1024\]{
  z-index: 1024;
}
.z-\[1025\]{
  z-index: 1025;
}
.z-\[1026\]{
  z-index: 1026;
}
.z-\[1027\]{
  z-index: 1027;
}
.z-\[1028\]{
  z-index: 1028;
}
.z-\[1030\]{
  z-index: 1030;
}
.z-\[1031\]{
  z-index: 1031;
}
.z-\[1034\]{
  z-index: 1034;
}
.z-\[1\]{
  z-index: 1;
}
.z-\[5\]{
  z-index: 5;
}
.z-\[995\]{
  z-index: 995;
}
.col-auto{
  grid-column: auto;
}
.col-span-12{
  grid-column: span 12 / span 12;
}
.col-span-3{
  grid-column: span 3 / span 3;
}
.col-span-4{
  grid-column: span 4 / span 4;
}
.col-span-5{
  grid-column: span 5 / span 5;
}
.col-span-6{
  grid-column: span 6 / span 6;
}
.col-span-7{
  grid-column: span 7 / span 7;
}
.col-span-8{
  grid-column: span 8 / span 8;
}
.col-end-12{
  grid-column-end: 12;
}
.float-end{
  float: inline-end;
}
.float-right{
  float: right;
}
.float-left{
  float: left;
}
.m-0{
  margin: 0px;
}
.m-0\.5{
  margin: 0.125rem;
}
.m-1{
  margin: 0.25rem;
}
.m-2{
  margin: 0.5rem;
}
.m-\[-10px_-10px_16px\]{
  margin: -10px -10px 16px;
}
.m-\[0_5px\]{
  margin: 0 5px;
}
.m-\[2px_0_0\]{
  margin: 2px 0 0;
}
.m-\[4px_14px\]{
  margin: 4px 14px;
}
.m-\[5px_7px\]{
  margin: 5px 7px;
}
.\!my-2{
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}
.-mx-3{
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}
.-my-5{
  margin-top: -1.25rem;
  margin-bottom: -1.25rem;
}
.-my-\[\.8rem\]{
  margin-top: -.8rem;
  margin-bottom: -.8rem;
}
.mx-0{
  margin-left: 0px;
  margin-right: 0px;
}
.mx-0\.5{
  margin-left: 0.125rem;
  margin-right: 0.125rem;
}
.mx-1{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-10{
  margin-left: 2.5rem;
  margin-right: 2.5rem;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-2\.5{
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}
.mx-3{
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.mx-\[15px\]{
  margin-left: 15px;
  margin-right: 15px;
}
.mx-\[5px\]{
  margin-left: 5px;
  margin-right: 5px;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-0{
  margin-top: 0px;
  margin-bottom: 0px;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-10{
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}
.my-2{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-2\.5{
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}
.my-3{
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-5{
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.\!mb-0{
  margin-bottom: 0px !important;
}
.-mb-\[1px\]{
  margin-bottom: -1px;
}
.-mr-1{
  margin-right: -0.25rem;
}
.-mt-\[20\%\]{
  margin-top: -20%;
}
.-mt-\[30px\]{
  margin-top: -30px;
}
.-mt-px{
  margin-top: -1px;
}
.mb-0{
  margin-bottom: 0px;
}
.mb-0\.5{
  margin-bottom: 0.125rem;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-12{
  margin-bottom: 3rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-2\.5{
  margin-bottom: 0.625rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-5{
  margin-bottom: 1.25rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.mb-\[14px\]{
  margin-bottom: 14px;
}
.mb-\[15px\]{
  margin-bottom: 15px;
}
.mb-\[5px\]{
  margin-bottom: 5px;
}
.me-0{
  margin-inline-end: 0px;
}
.me-1{
  margin-inline-end: 0.25rem;
}
.me-2{
  margin-inline-end: 0.5rem;
}
.me-3{
  margin-inline-end: 0.75rem;
}
.me-auto{
  margin-inline-end: auto;
}
.ml-0{
  margin-left: 0px;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-12{
  margin-left: 3rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-2\.5{
  margin-left: 0.625rem;
}
.ml-3{
  margin-left: 0.75rem;
}
.ml-4{
  margin-left: 1rem;
}
.ml-5{
  margin-left: 1.25rem;
}
.ml-\[140px\]{
  margin-left: 140px;
}
.ml-\[25px\]{
  margin-left: 25px;
}
.ml-\[57px\]{
  margin-left: 57px;
}
.ml-\[5px\]{
  margin-left: 5px;
}
.ml-\[70px\]{
  margin-left: 70px;
}
.ml-\[var\(--ck-image-style-spacing\)\]{
  margin-left: var(--ck-image-style-spacing);
}
.ml-auto{
  margin-left: auto;
}
.ml-sidebar-collapsed-active-width{
  margin-left: 300px;
}
.ml-sidebar-collapsed-width{
  margin-left: 100px;
}
.ml-sidebar-tab-navbar-width{
  margin-left: 320px;
}
.mr-0{
  margin-right: 0px;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-2\.5{
  margin-right: 0.625rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.mr-6{
  margin-right: 1.5rem;
}
.mr-auto{
  margin-right: auto;
}
.ms-1{
  margin-inline-start: 0.25rem;
}
.ms-2{
  margin-inline-start: 0.5rem;
}
.ms-3{
  margin-inline-start: 0.75rem;
}
.ms-4{
  margin-inline-start: 1rem;
}
.ms-auto{
  margin-inline-start: auto;
}
.mt-0{
  margin-top: 0px;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-12{
  margin-top: 3rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-2\.5{
  margin-top: 0.625rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.mt-\[-140px\]{
  margin-top: -140px;
}
.mt-header-height{
  margin-top: 74px;
}
.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.\!block{
  display: block !important;
}
.block{
  display: block;
}
.\!inline-block{
  display: inline-block !important;
}
.inline-block{
  display: inline-block;
}
.inline{
  display: inline;
}
.\!flex{
  display: flex !important;
}
.flex{
  display: flex;
}
.\!inline-flex{
  display: inline-flex !important;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.contents{
  display: contents;
}
.\!hidden{
  display: none !important;
}
.hidden{
  display: none;
}
.aspect-\[21\/9\]{
  aspect-ratio: 21/9;
}
.size-10{
  width: 2.5rem;
  height: 2.5rem;
}
.size-2{
  width: 0.5rem;
  height: 0.5rem;
}
.size-4{
  width: 1rem;
  height: 1rem;
}
.size-5{
  width: 1.25rem;
  height: 1.25rem;
}
.size-8{
  width: 2rem;
  height: 2rem;
}
.h-0\.5{
  height: 0.125rem;
}
.h-1{
  height: 0.25rem;
}
.h-1\.5{
  height: 0.375rem;
}
.h-10{
  height: 2.5rem;
}
.h-11{
  height: 2.75rem;
}
.h-12{
  height: 3rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-2\.5{
  height: 0.625rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-4{
  height: 1rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-7{
  height: 1.75rem;
}
.h-8{
  height: 2rem;
}
.h-9{
  height: 2.25rem;
}
.h-\[1\.25em\]{
  height: 1.25em;
}
.h-\[133px\]{
  height: 133px;
}
.h-\[14px\]{
  height: 14px;
}
.h-\[150px\]{
  height: 150px;
}
.h-\[15px\]{
  height: 15px;
}
.h-\[180px\]{
  height: 180px;
}
.h-\[18px\]{
  height: 18px;
}
.h-\[2\.5em\]{
  height: 2.5em;
}
.h-\[200px\]{
  height: 200px;
}
.h-\[22px\]{
  height: 22px;
}
.h-\[26px\]{
  height: 26px;
}
.h-\[27px\]{
  height: 27px;
}
.h-\[30px\]{
  height: 30px;
}
.h-\[320px\]{
  height: 320px;
}
.h-\[34px\]{
  height: 34px;
}
.h-\[45px\]{
  height: 45px;
}
.h-\[50px\]{
  height: 50px;
}
.h-\[55px\]{
  height: 55px;
}
.h-\[57px\]{
  height: 57px;
}
.h-\[5px\]{
  height: 5px;
}
.h-\[60px\]{
  height: 60px;
}
.h-\[9px\]{
  height: 9px;
}
.h-\[calc\(1\.5em_\+_1\.6rem_\+_2px\)\]{
  height: calc(1.5em + 1.6rem + 2px);
}
.h-\[calc\(100\%_-_30px\)\]{
  height: calc(100% - 30px);
}
.h-\[calc\(100\%_-_85px\)\]{
  height: calc(100% - 85px);
}
.h-\[calc\(100vh_-500px\)\]{
  height: calc(100vh - 500px);
}
.h-\[calc\(100vh_-_170px\)\]{
  height: calc(100vh - 170px);
}
.h-\[calc\(100vh_-_200px\)\]{
  height: calc(100vh - 200px);
}
.h-\[calc\(100vh_-_270px\)\]{
  height: calc(100vh - 270px);
}
.h-\[calc\(100vh_-_295px\)\]{
  height: calc(100vh - 295px);
}
.h-\[calc\(100vh_-_335px\)\]{
  height: calc(100vh - 335px);
}
.h-\[calc\(100vh_-_395px\)\]{
  height: calc(100vh - 395px);
}
.h-\[calc\(100vh_-_480px\)\]{
  height: calc(100vh - 480px);
}
.h-\[calc\(100vh_-_68px\)\]{
  height: calc(100vh - 68px);
}
.h-\[calc\(100vh_-_74px\)\]{
  height: calc(100vh - 74px);
}
.h-auto{
  height: auto;
}
.h-full{
  height: 100%;
}
.h-header-height{
  height: 74px;
}
.h-px{
  height: 1px;
}
.h-screen{
  height: 100vh;
}
.h-topbar-height{
  height: 60px;
}
.max-h-\[300px\]{
  max-height: 300px;
}
.max-h-\[calc\(100vh_-_100px\)\]{
  max-height: calc(100vh - 100px);
}
.min-h-\[15px\]{
  min-height: 15px;
}
.min-h-\[44px\]{
  min-height: 44px;
}
.min-h-\[auto\]{
  min-height: auto;
}
.min-h-\[calc\(100\%_-_5rem\)\]{
  min-height: calc(100% - 5rem);
}
.min-h-\[calc\(100vh_-_135px\)\]{
  min-height: calc(100vh - 135px);
}
.min-h-\[calc\(100vh_-_273px\)\]{
  min-height: calc(100vh - 273px);
}
.min-h-screen{
  min-height: 100vh;
}
.\!w-\[320px\]{
  width: 320px !important;
}
.\!w-\[550px\]{
  width: 550px !important;
}
.\!w-auto{
  width: auto !important;
}
.\!w-full{
  width: 100% !important;
}
.w-0{
  width: 0px;
}
.w-0\.5{
  width: 0.125rem;
}
.w-1\.5{
  width: 0.375rem;
}
.w-10{
  width: 2.5rem;
}
.w-11{
  width: 2.75rem;
}
.w-12{
  width: 3rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-2\.5{
  width: 0.625rem;
}
.w-20{
  width: 5rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-3\/4{
  width: 75%;
}
.w-4{
  width: 1rem;
}
.w-44{
  width: 11rem;
}
.w-5{
  width: 1.25rem;
}
.w-6{
  width: 1.5rem;
}
.w-7{
  width: 1.75rem;
}
.w-8{
  width: 2rem;
}
.w-8\/12{
  width: 66.666667%;
}
.w-9{
  width: 2.25rem;
}
.w-\[1\%\]{
  width: 1%;
}
.w-\[1\.25em\]{
  width: 1.25em;
}
.w-\[100px\]{
  width: 100px;
}
.w-\[115px\]{
  width: 115px;
}
.w-\[130px\]{
  width: 130px;
}
.w-\[14px\]{
  width: 14px;
}
.w-\[150px\]{
  width: 150px;
}
.w-\[15px\]{
  width: 15px;
}
.w-\[180px\]{
  width: 180px;
}
.w-\[18px\]{
  width: 18px;
}
.w-\[198px\]{
  width: 198px;
}
.w-\[200px\]{
  width: 200px;
}
.w-\[210px\]{
  width: 210px;
}
.w-\[22px\]{
  width: 22px;
}
.w-\[230px\]{
  width: 230px;
}
.w-\[26px\]{
  width: 26px;
}
.w-\[280px\]{
  width: 280px;
}
.w-\[2em\]{
  width: 2em;
}
.w-\[300\]{
  width: 300;
}
.w-\[300px\]{
  width: 300px;
}
.w-\[30px\]{
  width: 30px;
}
.w-\[310px\]{
  width: 310px;
}
.w-\[34px\]{
  width: 34px;
}
.w-\[35px\]{
  width: 35px;
}
.w-\[360px\]{
  width: 360px;
}
.w-\[375px\]{
  width: 375px;
}
.w-\[400px\]{
  width: 400px;
}
.w-\[420px\]{
  width: 420px;
}
.w-\[450px\]{
  width: 450px;
}
.w-\[45px\]{
  width: 45px;
}
.w-\[46px\]{
  width: 46px;
}
.w-\[474px\]{
  width: 474px;
}
.w-\[500px\]{
  width: 500px;
}
.w-\[50px\]{
  width: 50px;
}
.w-\[55px\]{
  width: 55px;
}
.w-\[560px\]{
  width: 560px;
}
.w-\[580px\]{
  width: 580px;
}
.w-\[60px\]{
  width: 60px;
}
.w-\[70px\]{
  width: 70px;
}
.w-\[75px\]{
  width: 75px;
}
.w-\[90px\]{
  width: 90px;
}
.w-\[9px\]{
  width: 9px;
}
.w-\[calc\(100\%_-_1\.4rem\)\]{
  width: calc(100% - 1.4rem);
}
.w-\[calc\(100\%_-_120px\)\]{
  width: calc(100% - 120px);
}
.w-\[calc\(100\%_-_20px\)\]{
  width: calc(100% - 20px);
}
.w-\[calc\(100\%_-_25px\)\]{
  width: calc(100% - 25px);
}
.w-\[calc\(100\%_-_30px\)\]{
  width: calc(100% - 30px);
}
.w-auto{
  width: auto;
}
.w-full{
  width: 100%;
}
.w-px{
  width: 1px;
}
.w-sidebar-collapsed-active-width{
  width: 300px;
}
.w-sidebar-collapsed-width{
  width: 100px;
}
.w-sidebar-tab-navbar-width{
  width: 320px;
}
.w-sidebar-tab-width{
  width: 75px;
}
.w-sidebar-width{
  width: 280px;
}
.min-w-0{
  min-width: 0px;
}
.min-w-48{
  min-width: 12rem;
}
.min-w-\[1\%\]{
  min-width: 1%;
}
.min-w-\[115px\]{
  min-width: 115px;
}
.min-w-\[15px\]{
  min-width: 15px;
}
.min-w-\[170px\]{
  min-width: 170px;
}
.min-w-\[186px\]{
  min-width: 186px;
}
.min-w-\[225px\]{
  min-width: 225px;
}
.min-w-\[3em\]{
  min-width: 3em;
}
.max-w-48{
  max-width: 12rem;
}
.max-w-64{
  max-width: 16rem;
}
.max-w-\[300px\]{
  max-width: 300px;
}
.max-w-\[480px\]{
  max-width: 480px;
}
.max-w-\[50\%\]{
  max-width: 50%;
}
.max-w-\[500px\]{
  max-width: 500px;
}
.max-w-\[800px\]{
  max-width: 800px;
}
.max-w-\[98\%\]{
  max-width: 98%;
}
.max-w-full{
  max-width: 100%;
}
.max-w-none{
  max-width: none;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-\[0_0_auto\]{
  flex: 0 0 auto;
}
.flex-\[1_0_0\%\]{
  flex: 1 0 0%;
}
.flex-auto{
  flex: 1 1 auto;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.shrink-0{
  flex-shrink: 0;
}
.grow{
  flex-grow: 1;
}
.basis-0{
  flex-basis: 0px;
}
.basis-auto{
  flex-basis: auto;
}
.basis-full{
  flex-basis: 100%;
}
.border-collapse{
  border-collapse: collapse;
}
.border-separate{
  border-collapse: separate;
}
.border-spacing-y-2\.5{
  --tw-border-spacing-y: 0.625rem;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
}
.origin-left{
  transform-origin: left;
}
.origin-top-left{
  transform-origin: top left;
}
.-translate-x-2\/4{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-2\/4{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-20{
  --tw-translate-y: -5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1\/4{
  --tw-translate-x: 25%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1\/4{
  --tw-translate-y: 25%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-90{
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[720deg\]{
  --tw-rotate: 720deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0{
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-150{
  --tw-scale-x: 1.5;
  --tw-scale-y: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-75{
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-\[1\.08\]{
  --tw-scale-x: 1.08;
  --tw-scale-y: 1.08;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-none{
  transform: none;
}
@keyframes progress-bar-stripes{
  0%{
    background-position-x: 1rem;
  }
}
.animate-\[1s_linear_infinite_progress-bar-stripes\]{
  animation: 1s linear infinite progress-bar-stripes;
}
@keyframes loader-animate{
  0%{
    left: -35%;
    right:  100%;
  }
  60%{
    left: 100%;
    right: -90%;
  }
  100%{
    left: 100%;
    right: -90%;
  }
}
.animate-\[2\.1s_cubic-bezier\(0\.65\2c 0\.815\2c 0\.735\2c 0\.395\)_0s_infinite_normal_none_running_loader-animate\]{
  animation: 2.1s cubic-bezier(0.65,0.815,0.735,0.395) 0s infinite normal none running loader-animate;
}
@keyframes btn-floating{
  0%{
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.3);
  }
  70%{
    box-shadow: 0 0 0 20px rgba(220, 38, 38, 0);
  }
  100%{
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
  }
}
.animate-\[btn-floating_2s_infinite\]{
  animation: btn-floating 2s infinite;
}
@keyframes move-bg{
  to{
    background-position: 400% 0;
  }
}
.animate-\[move-bg_24s_infinite_linear\]{
  animation: move-bg 24s infinite linear;
}
@keyframes slit{
  50%{
    transform: translateZ(-250px) rotateY(89deg);
    opacity: 1;
    animation-timing-function: ease-in;
  }
  100%{
    transform: translateZ(0) rotateY(0deg);
    opacity: 1;
  }
}
.animate-\[slit_0\.7s_forwards_ease-out\]{
  animation: slit 0.7s forwards ease-out;
}
@keyframes ping{
  75%, 100%{
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping{
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes spin{
  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-default{
  cursor: default;
}
.cursor-move{
  cursor: move;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
.cursor-pointer{
  cursor: pointer;
}
.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize{
  resize: both;
}
.list-inside{
  list-style-position: inside;
}
.list-\[circle\]{
  list-style-type: circle;
}
.list-decimal{
  list-style-type: decimal;
}
.list-disc{
  list-style-type: disc;
}
.list-none{
  list-style-type: none;
}
.appearance-none{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12{
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-\[repeat\(auto-fit\2c minmax\(8rem\2c 1fr\)\)\]{
  grid-template-columns: repeat(auto-fit,minmax(8rem,1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-row-reverse{
  flex-direction: row-reverse;
}
.flex-col{
  flex-direction: column;
}
.flex-wrap{
  flex-wrap: wrap;
}
.flex-nowrap{
  flex-wrap: nowrap;
}
.place-items-center{
  place-items: center;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-stretch{
  align-items: stretch;
}
.justify-start{
  justify-content: flex-start;
}
.\!justify-end{
  justify-content: flex-end !important;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-0{
  gap: 0px;
}
.gap-1{
  gap: 0.25rem;
}
.gap-1\.5{
  gap: 0.375rem;
}
.gap-12{
  gap: 3rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-x-2{
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.gap-x-3{
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}
.gap-x-4{
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
.gap-x-6{
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-y-3{
  row-gap: 0.75rem;
}
.gap-y-6{
  row-gap: 1.5rem;
}
.gap-y-8{
  row-gap: 2rem;
}
.-space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.-space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.-space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.divide-x > :not([hidden]) ~ :not([hidden]){
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}
.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-inherit > :not([hidden]) ~ :not([hidden]){
  border-color: inherit;
}
.divide-purple-300 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-divide-opacity));
}
.divide-theme-border > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-divide-opacity));
}
.divide-white\/40 > :not([hidden]) ~ :not([hidden]){
  border-color: rgb(255 255 255 / 0.4);
}
.self-center{
  align-self: center;
}
.self-stretch{
  align-self: stretch;
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.\!overflow-visible{
  overflow: visible !important;
}
.overflow-visible{
  overflow: visible;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.overflow-x-hidden{
  overflow-x: hidden;
}
.scroll-smooth{
  scroll-behavior: smooth;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-normal{
  white-space: normal;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.\!rounded-\[20px\]{
  border-radius: 20px !important;
}
.\!rounded-full{
  border-radius: 9999px !important;
}
.\!rounded-lg{
  border-radius: 0.5rem !important;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-\[0_12px_0_0\]{
  border-radius: 0 12px 0 0;
}
.rounded-\[10px\]{
  border-radius: 10px;
}
.rounded-\[20px\]{
  border-radius: 20px;
}
.rounded-\[6px\]{
  border-radius: 6px;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: 0.5rem;
}
.rounded-md{
  border-radius: 0.375rem;
}
.rounded-none{
  border-radius: 0px;
}
.rounded-xl{
  border-radius: 0.75rem;
}
.rounded-b-none{
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}
.rounded-b-xl{
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded-l-lg{
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-l-none{
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.rounded-r-none{
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.rounded-t-md{
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}
.rounded-t-none{
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}
.rounded-t-xl{
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.\!border-0{
  border-width: 0px !important;
}
.\!border-2{
  border-width: 2px !important;
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-\[2px\]{
  border-width: 2px;
}
.border-\[3\.5px\]{
  border-width: 3.5px;
}
.border-x{
  border-left-width: 1px;
  border-right-width: 1px;
}
.border-y{
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.\!border-b-0{
  border-bottom-width: 0px !important;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-0{
  border-bottom-width: 0px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-l{
  border-left-width: 1px;
}
.border-l-0{
  border-left-width: 0px;
}
.border-l-4{
  border-left-width: 4px;
}
.border-r{
  border-right-width: 1px;
}
.border-r-0{
  border-right-width: 0px;
}
.border-t{
  border-top-width: 1px;
}
.border-t-0{
  border-top-width: 0px;
}
.border-t-2{
  border-top-width: 2px;
}
.border-dashed{
  border-style: dashed;
}
.border-none{
  border-style: none;
}
.\!border-danger-500\/15{
  border-color: rgb(220 38 38 / 0.15) !important;
}
.\!border-info-500\/15{
  border-color: rgb(62 201 214 / 0.15) !important;
}
.\!border-primary-500{
  border-color: rgb(var(--colors-primary-500)) !important;
}
.\!border-purple-500\/15{
  border-color: rgb(168 85 247 / 0.15) !important;
}
.\!border-success-500\/15{
  border-color: rgb(44 168 127 / 0.15) !important;
}
.\!border-success-500\/30{
  border-color: rgb(44 168 127 / 0.3) !important;
}
.\!border-transparent{
  border-color: transparent !important;
}
.\!border-warning-500\/15{
  border-color: rgb(229 138 0 / 0.15) !important;
}
.\!border-white{
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 255 255 / var(--tw-border-opacity)) !important;
}
.border-danger{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}
.border-danger-500{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}
.border-danger-500\/20{
  border-color: rgb(220 38 38 / 0.2);
}
.border-dark{
  --tw-border-opacity: 1;
  border-color: rgb(33 37 41 / var(--tw-border-opacity));
}
.border-dark-500{
  --tw-border-opacity: 1;
  border-color: rgb(33 37 41 / var(--tw-border-opacity));
}
.border-dark-500\/20{
  border-color: rgb(33 37 41 / 0.2);
}
.border-info{
  --tw-border-opacity: 1;
  border-color: rgb(62 201 214 / var(--tw-border-opacity));
}
.border-info-500{
  --tw-border-opacity: 1;
  border-color: rgb(62 201 214 / var(--tw-border-opacity));
}
.border-info-500\/20{
  border-color: rgb(62 201 214 / 0.2);
}
.border-inherit{
  border-color: inherit;
}
.border-primary{
  border-color: rgb(var(--colors-primary));
}
.border-primary-500{
  border-color: rgb(var(--colors-primary-500));
}
.border-primary-500\/20{
  border-color: rgb(var(--colors-primary-500) / 0.2);
}
.border-secondary{
  --tw-border-opacity: 1;
  border-color: rgb(91 107 121 / var(--tw-border-opacity));
}
.border-secondary-500{
  --tw-border-opacity: 1;
  border-color: rgb(91 107 121 / var(--tw-border-opacity));
}
.border-secondary-500\/10{
  border-color: rgb(91 107 121 / 0.1);
}
.border-secondary-500\/20{
  border-color: rgb(91 107 121 / 0.2);
}
.border-success{
  --tw-border-opacity: 1;
  border-color: rgb(44 168 127 / var(--tw-border-opacity));
}
.border-success-500{
  --tw-border-opacity: 1;
  border-color: rgb(44 168 127 / var(--tw-border-opacity));
}
.border-success-500\/10{
  border-color: rgb(44 168 127 / 0.1);
}
.border-theme-border{
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.border-theme-cardbg{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-theme-inputborder{
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
}
.border-theme-sidebarbordercolor{
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
}
.border-themedark-border{
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.border-themedark-inputborder{
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.border-themedark-sidebarbordercolor{
  --tw-border-opacity: 1;
  border-color: rgb(36 45 57 / var(--tw-border-opacity));
}
.border-transparent{
  border-color: transparent;
}
.border-warning{
  --tw-border-opacity: 1;
  border-color: rgb(229 138 0 / var(--tw-border-opacity));
}
.border-warning-500{
  --tw-border-opacity: 1;
  border-color: rgb(229 138 0 / var(--tw-border-opacity));
}
.border-warning-500\/20{
  border-color: rgb(229 138 0 / 0.2);
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-y-theme-border{
  --tw-border-opacity: 1;
  border-top-color: rgb(231 234 238 / var(--tw-border-opacity));
  border-bottom-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.border-y-themedark-border{
  --tw-border-opacity: 1;
  border-top-color: rgb(48 63 80 / var(--tw-border-opacity));
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.\!border-b-danger-500{
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(220 38 38 / var(--tw-border-opacity)) !important;
}
.border-b-\[blue\]{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(0 0 255 / var(--tw-border-opacity));
}
.border-b-primary-500{
  border-bottom-color: rgb(var(--colors-primary-500));
}
.border-b-theme-bodycolor{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(19 25 32 / var(--tw-border-opacity));
}
.border-b-theme-border{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.border-b-themedark-bodycolor{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.border-b-themedark-border{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.border-l-danger-500{
  --tw-border-opacity: 1;
  border-left-color: rgb(220 38 38 / var(--tw-border-opacity));
}
.border-l-primary-500{
  border-left-color: rgb(var(--colors-primary-500));
}
.border-l-success-500{
  --tw-border-opacity: 1;
  border-left-color: rgb(44 168 127 / var(--tw-border-opacity));
}
.border-l-theme-border{
  --tw-border-opacity: 1;
  border-left-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.border-l-themedark-border{
  --tw-border-opacity: 1;
  border-left-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.border-l-transparent{
  border-left-color: transparent;
}
.border-r-primary-500{
  border-right-color: rgb(var(--colors-primary-500));
}
.border-r-theme-border{
  --tw-border-opacity: 1;
  border-right-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.border-r-themedark-border{
  --tw-border-opacity: 1;
  border-right-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.border-r-transparent{
  border-right-color: transparent;
}
.border-t-theme-bodycolor{
  --tw-border-opacity: 1;
  border-top-color: rgb(19 25 32 / var(--tw-border-opacity));
}
.border-t-theme-border{
  --tw-border-opacity: 1;
  border-top-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.border-t-themedark-bodycolor{
  --tw-border-opacity: 1;
  border-top-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.border-t-themedark-border{
  --tw-border-opacity: 1;
  border-top-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.\!bg-danger-500{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity)) !important;
}
.\!bg-primary-500{
  background-color: rgb(var(--colors-primary-500)) !important;
}
.\!bg-secondary-500\/10{
  background-color: rgb(91 107 121 / 0.1) !important;
}
.\!bg-success-500{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity)) !important;
}
.\!bg-transparent{
  background-color: transparent !important;
}
.bg-\[\#0077b5\]\/10{
  background-color: rgb(0 119 181 / 0.1);
}
.bg-\[\#4267b2\]\/10{
  background-color: rgb(66 103 178 / 0.1);
}
.bg-\[\#42c0fb\]\/10{
  background-color: rgb(66 192 251 / 0.1);
}
.bg-\[\#478f3b\]{
  --tw-bg-opacity: 1;
  background-color: rgb(71 143 59 / var(--tw-bg-opacity));
}
.bg-\[\#8283ff\]{
  --tw-bg-opacity: 1;
  background-color: rgb(130 131 255 / var(--tw-bg-opacity));
}
.bg-\[\#999\]{
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity));
}
.bg-\[\#99cc00\]{
  --tw-bg-opacity: 1;
  background-color: rgb(153 204 0 / var(--tw-bg-opacity));
}
.bg-\[\#b3b3b3\]{
  --tw-bg-opacity: 1;
  background-color: rgb(179 179 179 / var(--tw-bg-opacity));
}
.bg-\[\#ddd\]{
  --tw-bg-opacity: 1;
  background-color: rgb(221 221 221 / var(--tw-bg-opacity));
}
.bg-\[\#f70616\]{
  --tw-bg-opacity: 1;
  background-color: rgb(247 6 22 / var(--tw-bg-opacity));
}
.bg-\[\#ff8282\]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 130 130 / var(--tw-bg-opacity));
}
.bg-\[\#ff9900\]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 153 0 / var(--tw-bg-opacity));
}
.bg-\[green\]{
  --tw-bg-opacity: 1;
  background-color: rgb(0 128 0 / var(--tw-bg-opacity));
}
.bg-\[red\]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 0 / var(--tw-bg-opacity));
}
.bg-\[rgb\(128\2c _128\2c _128\)\]{
  --tw-bg-opacity: 1;
  background-color: rgb(128 128 128 / var(--tw-bg-opacity));
}
.bg-\[rgba\(0\2c 0\2c 0\2c \.15\)\]{
  background-color: rgba(0,0,0,.15);
}
.bg-\[rgba\(0\2c 0\2c 0\2c 0\.1\)\]{
  background-color: rgba(0,0,0,0.1);
}
.bg-\[rgba\(248\2c 249\2c 250\2c 0\.5\)\]{
  background-color: rgba(248,249,250,0.5);
}
.bg-amber-500{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}
.bg-cyan-500{
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
}
.bg-danger{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
.bg-danger-100{
  --tw-bg-opacity: 1;
  background-color: rgb(245 190 190 / var(--tw-bg-opacity));
}
.bg-danger-200{
  --tw-bg-opacity: 1;
  background-color: rgb(238 147 147 / var(--tw-bg-opacity));
}
.bg-danger-300{
  --tw-bg-opacity: 1;
  background-color: rgb(231 103 103 / var(--tw-bg-opacity));
}
.bg-danger-400{
  --tw-bg-opacity: 1;
  background-color: rgb(225 71 71 / var(--tw-bg-opacity));
}
.bg-danger-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 222 222 / var(--tw-bg-opacity));
}
.bg-danger-500{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
.bg-danger-500\/0{
  background-color: rgb(220 38 38 / 0);
}
.bg-danger-500\/10{
  background-color: rgb(220 38 38 / 0.1);
}
.bg-danger-500\/15{
  background-color: rgb(220 38 38 / 0.15);
}
.bg-danger-500\/20{
  background-color: rgb(220 38 38 / 0.2);
}
.bg-danger-600{
  --tw-bg-opacity: 1;
  background-color: rgb(216 34 34 / var(--tw-bg-opacity));
}
.bg-danger-700{
  --tw-bg-opacity: 1;
  background-color: rgb(211 28 28 / var(--tw-bg-opacity));
}
.bg-danger-800{
  --tw-bg-opacity: 1;
  background-color: rgb(206 23 23 / var(--tw-bg-opacity));
}
.bg-danger-900{
  --tw-bg-opacity: 1;
  background-color: rgb(197 13 13 / var(--tw-bg-opacity));
}
.bg-danger-950{
  --tw-bg-opacity: 1;
  background-color: rgb(165 9 9 / var(--tw-bg-opacity));
}
.bg-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.bg-dark-100{
  --tw-bg-opacity: 1;
  background-color: rgb(206 210 215 / var(--tw-bg-opacity));
}
.bg-dark-200{
  --tw-bg-opacity: 1;
  background-color: rgb(158 167 177 / var(--tw-bg-opacity));
}
.bg-dark-300{
  --tw-bg-opacity: 1;
  background-color: rgb(110 124 137 / var(--tw-bg-opacity));
}
.bg-dark-400{
  --tw-bg-opacity: 1;
  background-color: rgb(72 80 89 / var(--tw-bg-opacity));
}
.bg-dark-50{
  --tw-bg-opacity: 1;
  background-color: rgb(230 232 235 / var(--tw-bg-opacity));
}
.bg-dark-500{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.bg-dark-500\/0{
  background-color: rgb(33 37 41 / 0);
}
.bg-dark-500\/10{
  background-color: rgb(33 37 41 / 0.1);
}
.bg-dark-500\/15{
  background-color: rgb(33 37 41 / 0.15);
}
.bg-dark-500\/20{
  background-color: rgb(33 37 41 / 0.2);
}
.bg-dark-500\/40{
  background-color: rgb(33 37 41 / 0.4);
}
.bg-dark-600{
  --tw-bg-opacity: 1;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity));
}
.bg-dark-700{
  --tw-bg-opacity: 1;
  background-color: rgb(19 22 24 / var(--tw-bg-opacity));
}
.bg-dark-800{
  --tw-bg-opacity: 1;
  background-color: rgb(13 14 16 / var(--tw-bg-opacity));
}
.bg-dark-900{
  --tw-bg-opacity: 1;
  background-color: rgb(6 6 7 / var(--tw-bg-opacity));
}
.bg-dark-950{
  --tw-bg-opacity: 1;
  background-color: rgb(2 3 3 / var(--tw-bg-opacity));
}
.bg-emerald-500{
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity));
}
.bg-fuchsia-500{
  --tw-bg-opacity: 1;
  background-color: rgb(217 70 239 / var(--tw-bg-opacity));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}
.bg-gray-800{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
.bg-gray-900\/20{
  background-color: rgb(17 24 39 / 0.2);
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}
.bg-indigo-500{
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity));
}
.bg-info{
  --tw-bg-opacity: 1;
  background-color: rgb(62 201 214 / var(--tw-bg-opacity));
}
.bg-info-100{
  --tw-bg-opacity: 1;
  background-color: rgb(197 239 243 / var(--tw-bg-opacity));
}
.bg-info-200{
  --tw-bg-opacity: 1;
  background-color: rgb(159 228 235 / var(--tw-bg-opacity));
}
.bg-info-300{
  --tw-bg-opacity: 1;
  background-color: rgb(120 217 226 / var(--tw-bg-opacity));
}
.bg-info-400{
  --tw-bg-opacity: 1;
  background-color: rgb(91 209 220 / var(--tw-bg-opacity));
}
.bg-info-50{
  --tw-bg-opacity: 1;
  background-color: rgb(226 247 249 / var(--tw-bg-opacity));
}
.bg-info-500{
  --tw-bg-opacity: 1;
  background-color: rgb(62 201 214 / var(--tw-bg-opacity));
}
.bg-info-500\/0{
  background-color: rgb(62 201 214 / 0);
}
.bg-info-500\/10{
  background-color: rgb(62 201 214 / 0.1);
}
.bg-info-500\/15{
  background-color: rgb(62 201 214 / 0.15);
}
.bg-info-500\/20{
  background-color: rgb(62 201 214 / 0.2);
}
.bg-info-600{
  --tw-bg-opacity: 1;
  background-color: rgb(56 195 209 / var(--tw-bg-opacity));
}
.bg-info-700{
  --tw-bg-opacity: 1;
  background-color: rgb(48 188 204 / var(--tw-bg-opacity));
}
.bg-info-800{
  --tw-bg-opacity: 1;
  background-color: rgb(40 181 198 / var(--tw-bg-opacity));
}
.bg-info-900{
  --tw-bg-opacity: 1;
  background-color: rgb(27 169 188 / var(--tw-bg-opacity));
}
.bg-info-950{
  --tw-bg-opacity: 1;
  background-color: rgb(20 137 153 / var(--tw-bg-opacity));
}
.bg-inherit{
  background-color: inherit;
}
.bg-lime-500{
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity));
}
.bg-neutral-500{
  --tw-bg-opacity: 1;
  background-color: rgb(115 115 115 / var(--tw-bg-opacity));
}
.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}
.bg-pink-500{
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity));
}
.bg-primary{
  background-color: rgb(var(--colors-primary));
}
.bg-primary-100{
  background-color: rgb(var(--colors-primary-100));
}
.bg-primary-100\/50{
  background-color: rgb(var(--colors-primary-100) / 0.5);
}
.bg-primary-200{
  background-color: rgb(var(--colors-primary-200));
}
.bg-primary-300{
  background-color: rgb(var(--colors-primary-300));
}
.bg-primary-400{
  background-color: rgb(var(--colors-primary-400));
}
.bg-primary-50{
  background-color: rgb(var(--colors-primary-50));
}
.bg-primary-50\/20{
  background-color: rgb(var(--colors-primary-50) / 0.2);
}
.bg-primary-500{
  background-color: rgb(var(--colors-primary-500));
}
.bg-primary-500\/0{
  background-color: rgb(var(--colors-primary-500) / 0);
}
.bg-primary-500\/10{
  background-color: rgb(var(--colors-primary-500) / 0.1);
}
.bg-primary-500\/15{
  background-color: rgb(var(--colors-primary-500) / 0.15);
}
.bg-primary-500\/20{
  background-color: rgb(var(--colors-primary-500) / 0.2);
}
.bg-primary-600{
  background-color: rgb(var(--colors-primary-600));
}
.bg-primary-700{
  background-color: rgb(var(--colors-primary-700));
}
.bg-primary-800{
  background-color: rgb(var(--colors-primary-800));
}
.bg-primary-900{
  background-color: rgb(var(--colors-primary-900));
}
.bg-primary-950{
  background-color: rgb(var(--colors-primary-950));
}
.bg-purple-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}
.bg-rose-500{
  --tw-bg-opacity: 1;
  background-color: rgb(244 63 94 / var(--tw-bg-opacity));
}
.bg-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.bg-secondary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(225 229 232 / var(--tw-bg-opacity));
}
.bg-secondary-100\/10{
  background-color: rgb(225 229 232 / 0.1);
}
.bg-secondary-100\/20{
  background-color: rgb(225 229 232 / 0.2);
}
.bg-secondary-200{
  --tw-bg-opacity: 1;
  background-color: rgb(190 198 206 / var(--tw-bg-opacity));
}
.bg-secondary-300{
  --tw-bg-opacity: 1;
  background-color: rgb(155 168 180 / var(--tw-bg-opacity));
}
.bg-secondary-300\/10{
  background-color: rgb(155 168 180 / 0.1);
}
.bg-secondary-400{
  --tw-bg-opacity: 1;
  background-color: rgb(120 138 153 / var(--tw-bg-opacity));
}
.bg-secondary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(242 244 245 / var(--tw-bg-opacity));
}
.bg-secondary-50\/10{
  background-color: rgb(242 244 245 / 0.1);
}
.bg-secondary-50\/20{
  background-color: rgb(242 244 245 / 0.2);
}
.bg-secondary-500{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.bg-secondary-500\/0{
  background-color: rgb(91 107 121 / 0);
}
.bg-secondary-500\/10{
  background-color: rgb(91 107 121 / 0.1);
}
.bg-secondary-500\/15{
  background-color: rgb(91 107 121 / 0.15);
}
.bg-secondary-500\/20{
  background-color: rgb(91 107 121 / 0.2);
}
.bg-secondary-500\/30{
  background-color: rgb(91 107 121 / 0.3);
}
.bg-secondary-500\/35{
  background-color: rgb(91 107 121 / 0.35);
}
.bg-secondary-600{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
}
.bg-secondary-700{
  --tw-bg-opacity: 1;
  background-color: rgb(56 66 74 / var(--tw-bg-opacity));
}
.bg-secondary-800{
  --tw-bg-opacity: 1;
  background-color: rgb(38 45 51 / var(--tw-bg-opacity));
}
.bg-secondary-900{
  --tw-bg-opacity: 1;
  background-color: rgb(21 25 28 / var(--tw-bg-opacity));
}
.bg-secondary-950{
  --tw-bg-opacity: 1;
  background-color: rgb(12 14 16 / var(--tw-bg-opacity));
}
.bg-sky-500{
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity));
}
.bg-slate-500{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}
.bg-slate-500\/30{
  background-color: rgb(100 116 139 / 0.3);
}
.bg-stone-500{
  --tw-bg-opacity: 1;
  background-color: rgb(120 113 108 / var(--tw-bg-opacity));
}
.bg-success{
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
}
.bg-success-100{
  --tw-bg-opacity: 1;
  background-color: rgb(192 229 217 / var(--tw-bg-opacity));
}
.bg-success-200{
  --tw-bg-opacity: 1;
  background-color: rgb(150 212 191 / var(--tw-bg-opacity));
}
.bg-success-300{
  --tw-bg-opacity: 1;
  background-color: rgb(107 194 165 / var(--tw-bg-opacity));
}
.bg-success-400{
  --tw-bg-opacity: 1;
  background-color: rgb(76 181 146 / var(--tw-bg-opacity));
}
.bg-success-50{
  --tw-bg-opacity: 1;
  background-color: rgb(223 242 236 / var(--tw-bg-opacity));
}
.bg-success-500{
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
}
.bg-success-500\/0{
  background-color: rgb(44 168 127 / 0);
}
.bg-success-500\/10{
  background-color: rgb(44 168 127 / 0.1);
}
.bg-success-500\/15{
  background-color: rgb(44 168 127 / 0.15);
}
.bg-success-500\/20{
  background-color: rgb(44 168 127 / 0.2);
}
.bg-success-600{
  --tw-bg-opacity: 1;
  background-color: rgb(39 160 119 / var(--tw-bg-opacity));
}
.bg-success-700{
  --tw-bg-opacity: 1;
  background-color: rgb(33 151 108 / var(--tw-bg-opacity));
}
.bg-success-800{
  --tw-bg-opacity: 1;
  background-color: rgb(27 141 98 / var(--tw-bg-opacity));
}
.bg-success-900{
  --tw-bg-opacity: 1;
  background-color: rgb(16 125 79 / var(--tw-bg-opacity));
}
.bg-success-950{
  --tw-bg-opacity: 1;
  background-color: rgb(10 87 54 / var(--tw-bg-opacity));
}
.bg-teal-500{
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity));
}
.bg-theme-bodybg{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity));
}
.bg-theme-bodycolor{
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.bg-theme-border{
  --tw-bg-opacity: 1;
  background-color: rgb(231 234 238 / var(--tw-bg-opacity));
}
.bg-theme-cardbg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-theme-headerbg{
  background-color: rgba( 248,249,250, 0.7);
}
.bg-theme-horizontalsubmenubg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-theme-inputbg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-theme-sidebarcolor{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.bg-theme-sidebaruserbg{
  --tw-bg-opacity: 1;
  background-color: rgb(243 245 247 / var(--tw-bg-opacity));
}
.bg-themedark-activebg{
  --tw-bg-opacity: 1;
  background-color: rgb(25 33 42 / var(--tw-bg-opacity));
}
.bg-themedark-bodybg{
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.bg-themedark-bodycolor{
  --tw-bg-opacity: 1;
  background-color: rgb(191 191 191 / var(--tw-bg-opacity));
}
.bg-themedark-border{
  --tw-bg-opacity: 1;
  background-color: rgb(48 63 80 / var(--tw-bg-opacity));
}
.bg-themedark-cardbg{
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.bg-themedark-inputbg{
  --tw-bg-opacity: 1;
  background-color: rgb(38 50 64 / var(--tw-bg-opacity));
}
.bg-transparent{
  background-color: transparent;
}
.bg-violet-500{
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity));
}
.bg-warning{
  --tw-bg-opacity: 1;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity));
}
.bg-warning-100{
  --tw-bg-opacity: 1;
  background-color: rgb(247 220 179 / var(--tw-bg-opacity));
}
.bg-warning-200{
  --tw-bg-opacity: 1;
  background-color: rgb(242 197 128 / var(--tw-bg-opacity));
}
.bg-warning-300{
  --tw-bg-opacity: 1;
  background-color: rgb(237 173 77 / var(--tw-bg-opacity));
}
.bg-warning-400{
  --tw-bg-opacity: 1;
  background-color: rgb(233 156 38 / var(--tw-bg-opacity));
}
.bg-warning-50{
  --tw-bg-opacity: 1;
  background-color: rgb(251 237 217 / var(--tw-bg-opacity));
}
.bg-warning-500{
  --tw-bg-opacity: 1;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity));
}
.bg-warning-500\/0{
  background-color: rgb(229 138 0 / 0);
}
.bg-warning-500\/10{
  background-color: rgb(229 138 0 / 0.1);
}
.bg-warning-500\/15{
  background-color: rgb(229 138 0 / 0.15);
}
.bg-warning-500\/20{
  background-color: rgb(229 138 0 / 0.2);
}
.bg-warning-600{
  --tw-bg-opacity: 1;
  background-color: rgb(226 130 0 / var(--tw-bg-opacity));
}
.bg-warning-700{
  --tw-bg-opacity: 1;
  background-color: rgb(222 119 0 / var(--tw-bg-opacity));
}
.bg-warning-800{
  --tw-bg-opacity: 1;
  background-color: rgb(218 109 0 / var(--tw-bg-opacity));
}
.bg-warning-900{
  --tw-bg-opacity: 1;
  background-color: rgb(211 90 0 / var(--tw-bg-opacity));
}
.bg-warning-950{
  --tw-bg-opacity: 1;
  background-color: rgb(181 77 0 / var(--tw-bg-opacity));
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-white\/10{
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/30{
  background-color: rgb(255 255 255 / 0.3);
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}
.bg-zinc-500{
  --tw-bg-opacity: 1;
  background-color: rgb(113 113 122 / var(--tw-bg-opacity));
}
.bg-opacity-10{
  --tw-bg-opacity: 0.1;
}
.bg-opacity-25{
  --tw-bg-opacity: 0.25;
}
.bg-opacity-75{
  --tw-bg-opacity: 0.75;
}
.bg-\[linear-gradient\(213\.66deg\2c transparent_25\.46\%\2c rgba\(0\2c 0\2c 0\2c 0\.2\)_68\.77\%\2c rgba\(0\2c 0\2c 0\2c 0\.3\)_81\.72\%\)\]{
  background-image: linear-gradient(213.66deg,transparent 25.46%,rgba(0,0,0,0.2) 68.77%,rgba(0,0,0,0.3) 81.72%);
}
.bg-\[linear-gradient\(245deg\2c transparent_25\.46\%\2c rgba\(0\2c 0\2c 0\2c 0\.2\)_68\.77\%\2c rgba\(0\2c 0\2c 0\2c 0\.3\)_81\.72\%\)\]{
  background-image: linear-gradient(245deg,transparent 25.46%,rgba(0,0,0,0.2) 68.77%,rgba(0,0,0,0.3) 81.72%);
}
.bg-\[linear-gradient\(45deg\2c rgba\(255\2c 255\2c 255\2c \.15\)_25\%\2c transparent_25\%\2c transparent_50\%\2c rgba\(255\2c 255\2c 255\2c \.15\)_50\%\2c rgba\(255\2c 255\2c 255\2c \.15\)_75\%\2c transparent_75\%\2c transparent\)\]{
  background-image: linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
}
.bg-\[linear-gradient\(to_bottom\2c _\#dfdfdf_0\2c _\#bebebe_100\%\)\]{
  background-image: linear-gradient(to bottom, #dfdfdf 0, #bebebe 100%);
}
.bg-\[linear-gradient\(to_bottom\2c _\#f8f9fa_0\2c _\#f8f9fa_100\%\)\]{
  background-image: linear-gradient(to bottom, #f8f9fa 0, #f8f9fa 100%);
}
.bg-\[linear-gradient\(to_bottom\2c _\#f9f9f9_0\2c _\#f5f5f5_100\%\)\]{
  background-image: linear-gradient(to bottom, #f9f9f9 0, #f5f5f5 100%);
}
.bg-\[url\(\"\.\.\/images\/application\/img-cal-bg\.jpg\"\)\]{
  background-image: url("../images/application/img-cal-bg.jpg");
}
.bg-\[url\(\'\.\.\/images\/authentication\/img-auth-bg\.jpg\'\)\]{
  background-image: url('../images/authentication/img-auth-bg.jpg');
}
.bg-\[url\(\'\.\.\/images\/landing\/img-headerbg\.jpg\'\)\]{
  background-image: url('../images/landing/img-headerbg.jpg');
}
.bg-\[url\(\'\.\.\/images\/pages\/img-cunstruct-1-bg\.png\'\)\]{
  background-image: url('../images/pages/img-cunstruct-1-bg.png');
}
.bg-\[url\(\'\.\.\/images\/widget\/img-dropbox-bg\.svg\'\)\]{
  background-image: url('../images/widget/img-dropbox-bg.svg');
}
.bg-\[url\(\.\.\/images\/landing\/img-headerbg\.jpg\)\]{
  background-image: url(../images/landing/img-headerbg.jpg);
}
.bg-\[url\(\.\.\/images\/widget\/img-card-bg\.svg\)\]{
  background-image: url(../images/widget/img-card-bg.svg);
}
.bg-checkbox-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.bg-choice-close-btn{
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-none{
  background-image: none;
}
.bg-radio-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e");
}
.bg-select-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%231d2630' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
.bg-switch-active-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
}
.bg-switch-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
}
.from-\[rgb\(37\2c 161\2c 244\)\]{
  --tw-gradient-from: rgb(37,161,244) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 161 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-600{
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600{
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-600{
  --tw-gradient-from: #0891b2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(8 145 178 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-danger-600{
  --tw-gradient-from: #d82222 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(216 34 34 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-dark-600{
  --tw-gradient-from: #1A1D21 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 29 33 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-600{
  --tw-gradient-from: #059669 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-fuchsia-600{
  --tw-gradient-from: #c026d3 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(192 38 211 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-600{
  --tw-gradient-from: #4b5563 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(75 85 99 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-600{
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-600{
  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-info-600{
  --tw-gradient-from: #38c3d1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(56 195 209 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-lime-600{
  --tw-gradient-from: #65a30d var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(101 163 13 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-neutral-600{
  --tw-gradient-from: #525252 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(82 82 82 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-600{
  --tw-gradient-from: #ea580c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-600{
  --tw-gradient-from: #db2777 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 39 119 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-600{
  --tw-gradient-from: rgb(var(--colors-primary-600)) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(var(--colors-primary-600) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600{
  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-600{
  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-rose-600{
  --tw-gradient-from: #e11d48 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(225 29 72 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-secondary-600{
  --tw-gradient-from: #495662 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(73 86 98 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sky-600{
  --tw-gradient-from: #0284c7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-600{
  --tw-gradient-from: #475569 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(71 85 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-stone-600{
  --tw-gradient-from: #57534e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(87 83 78 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-success-600{
  --tw-gradient-from: #27a077 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(39 160 119 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-teal-600{
  --tw-gradient-from: #0d9488 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(13 148 136 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-violet-600{
  --tw-gradient-from: #7c3aed var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(124 58 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-warning-600{
  --tw-gradient-from: #e28200 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(226 130 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-600{
  --tw-gradient-from: #ca8a04 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(202 138 4 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-zinc-600{
  --tw-gradient-from: #52525b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(82 82 91 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-\[rgb\(249\2c 31\2c 169\)\]{
  --tw-gradient-to: rgb(249 31 169 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(249,31,169) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-\[rgb\(37\2c 161\2c 244\)\]{
  --tw-gradient-to: rgb(37,161,244) var(--tw-gradient-to-position);
}
.to-amber-400{
  --tw-gradient-to: #fbbf24 var(--tw-gradient-to-position);
}
.to-blue-400{
  --tw-gradient-to: #60a5fa var(--tw-gradient-to-position);
}
.to-cyan-400{
  --tw-gradient-to: #22d3ee var(--tw-gradient-to-position);
}
.to-danger-400{
  --tw-gradient-to: #e14747 var(--tw-gradient-to-position);
}
.to-dark-400{
  --tw-gradient-to: #485059 var(--tw-gradient-to-position);
}
.to-emerald-400{
  --tw-gradient-to: #34d399 var(--tw-gradient-to-position);
}
.to-fuchsia-400{
  --tw-gradient-to: #e879f9 var(--tw-gradient-to-position);
}
.to-gray-400{
  --tw-gradient-to: #9ca3af var(--tw-gradient-to-position);
}
.to-green-400{
  --tw-gradient-to: #4ade80 var(--tw-gradient-to-position);
}
.to-indigo-400{
  --tw-gradient-to: #818cf8 var(--tw-gradient-to-position);
}
.to-info-400{
  --tw-gradient-to: #5bd1dc var(--tw-gradient-to-position);
}
.to-lime-400{
  --tw-gradient-to: #a3e635 var(--tw-gradient-to-position);
}
.to-neutral-400{
  --tw-gradient-to: #a3a3a3 var(--tw-gradient-to-position);
}
.to-orange-400{
  --tw-gradient-to: #fb923c var(--tw-gradient-to-position);
}
.to-pink-400{
  --tw-gradient-to: #f472b6 var(--tw-gradient-to-position);
}
.to-primary-400{
  --tw-gradient-to: rgb(var(--colors-primary-400)) var(--tw-gradient-to-position);
}
.to-purple-400{
  --tw-gradient-to: #c084fc var(--tw-gradient-to-position);
}
.to-red-400{
  --tw-gradient-to: #f87171 var(--tw-gradient-to-position);
}
.to-rose-400{
  --tw-gradient-to: #fb7185 var(--tw-gradient-to-position);
}
.to-secondary-400{
  --tw-gradient-to: #788A99 var(--tw-gradient-to-position);
}
.to-sky-400{
  --tw-gradient-to: #38bdf8 var(--tw-gradient-to-position);
}
.to-slate-400{
  --tw-gradient-to: #94a3b8 var(--tw-gradient-to-position);
}
.to-stone-400{
  --tw-gradient-to: #a8a29e var(--tw-gradient-to-position);
}
.to-success-400{
  --tw-gradient-to: #4cb592 var(--tw-gradient-to-position);
}
.to-teal-400{
  --tw-gradient-to: #2dd4bf var(--tw-gradient-to-position);
}
.to-violet-400{
  --tw-gradient-to: #a78bfa var(--tw-gradient-to-position);
}
.to-warning-400{
  --tw-gradient-to: #e99c26 var(--tw-gradient-to-position);
}
.to-yellow-400{
  --tw-gradient-to: #facc15 var(--tw-gradient-to-position);
}
.to-zinc-400{
  --tw-gradient-to: #a1a1aa var(--tw-gradient-to-position);
}
.bg-\[length\:100\%\]{
  background-size: 100%;
}
.bg-\[length\:16px_12px\]{
  background-size: 16px 12px;
}
.bg-\[length\:16px_16px\]{
  background-size: 16px 16px;
}
.bg-\[length\:400\%_100\%\]{
  background-size: 400% 100%;
}
.bg-\[length\:8px_8px\]{
  background-size: 8px 8px;
}
.bg-contain{
  background-size: contain;
}
.bg-cover{
  background-size: cover;
}
.bg-clip-text{
  -webkit-background-clip: text;
          background-clip: text;
}
.bg-center{
  background-position: center;
}
.bg-left{
  background-position: left;
}
.bg-left-top{
  background-position: left top;
}
.bg-right-bottom{
  background-position: right bottom;
}
.bg-no-repeat{
  background-repeat: no-repeat;
}
.bg-repeat-x{
  background-repeat: repeat-x;
}
.\!fill-themedark-bodycolor{
  fill: #bfbfbf !important;
}
.\!fill-themedark-inputbg{
  fill: #263240 !important;
}
.fill-danger-500\/10{
  fill: rgb(220 38 38 / 0.1);
}
.fill-primary-500\/10{
  fill: rgb(var(--colors-primary-500) / 0.1);
}
.fill-secondary-500\/10{
  fill: rgb(91 107 121 / 0.1);
}
.fill-success-500\/10{
  fill: rgb(44 168 127 / 0.1);
}
.fill-warning-500\/10{
  fill: rgb(229 138 0 / 0.1);
}
.stroke-danger-500{
  stroke: #dc2626;
}
.stroke-primary-500{
  stroke: rgb(var(--colors-primary-500));
}
.stroke-secondary-500{
  stroke: #5B6B79;
}
.stroke-success-500{
  stroke: #2ca87f;
}
.stroke-theme-bodycolor{
  stroke: #131920;
}
.stroke-themedark-bodycolor{
  stroke: #bfbfbf;
}
.stroke-warning-500{
  stroke: #e58a00;
}
.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}
.object-bottom{
  -o-object-position: bottom;
     object-position: bottom;
}
.\!p-0{
  padding: 0px !important;
}
.\!p-10{
  padding: 2.5rem !important;
}
.\!p-2{
  padding: 0.5rem !important;
}
.\!p-3{
  padding: 0.75rem !important;
}
.\!p-4{
  padding: 1rem !important;
}
.\!p-5{
  padding: 1.25rem !important;
}
.\!p-\[12px_30px\]{
  padding: 12px 30px !important;
}
.p-0{
  padding: 0px;
}
.p-0\.5{
  padding: 0.125rem;
}
.p-1{
  padding: 0.25rem;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-2{
  padding: 0.5rem;
}
.p-2\.5{
  padding: 0.625rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-5{
  padding: 1.25rem;
}
.p-6{
  padding: 1.5rem;
}
.p-\[\.4rem_\.8rem\]{
  padding: .4rem .8rem;
}
.p-\[\.6rem_\.8rem\]{
  padding: .6rem .8rem;
}
.p-\[\.7rem_\.75rem\]{
  padding: .7rem .75rem;
}
.p-\[\.8rem\]{
  padding: .8rem;
}
.p-\[\.8rem_\.75rem\]{
  padding: .8rem .75rem;
}
.p-\[\.9rem_\.75rem\]{
  padding: .9rem .75rem;
}
.p-\[\.9rem_\.8rem\]{
  padding: .9rem .8rem;
}
.p-\[0_1em\]{
  padding: 0 1em;
}
.p-\[1\.25rem_\.8rem\]{
  padding: 1.25rem .8rem;
}
.p-\[10px_14px\]{
  padding: 10px 14px;
}
.p-\[10px_16px\]{
  padding: 10px 16px;
}
.p-\[10px_16px_10px_30px\]{
  padding: 10px 16px 10px 30px;
}
.p-\[10px_16px_10px_45px\]{
  padding: 10px 16px 10px 45px;
}
.p-\[12px_15px_12px_20px\]{
  padding: 12px 15px 12px 20px;
}
.p-\[12px_30px_12px_45px\]{
  padding: 12px 30px 12px 45px;
}
.p-\[12px_30px_12px_52px\]{
  padding: 12px 30px 12px 52px;
}
.p-\[12px_30px_12px_70px\]{
  padding: 12px 30px 12px 70px;
}
.p-\[14px\]{
  padding: 14px;
}
.p-\[14px_12px\]{
  padding: 14px 12px;
}
.p-\[16px_12px\]{
  padding: 16px 12px;
}
.p-\[18px_0_10px\]{
  padding: 18px 0 10px;
}
.p-\[20px_18px\]{
  padding: 20px 18px;
}
.p-\[25px\]{
  padding: 25px;
}
.p-\[3px\]{
  padding: 3px;
}
.p-\[3px_20px\]{
  padding: 3px 20px;
}
.p-\[5px\]{
  padding: 5px;
}
.p-\[5px_-\]{
  padding: 5px -;
}
.p-\[6px_20px\]{
  padding: 6px 20px;
}
.p-\[9px_16px\]{
  padding: 9px 16px;
}
.\!px-0{
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.\!px-3{
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}
.\!px-\[25px\]{
  padding-left: 25px !important;
  padding-right: 25px !important;
}
.\!py-0{
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.\!py-1{
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}
.\!py-2{
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}
.\!py-3{
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}
.\!py-4{
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}
.px-0{
  padding-left: 0px;
  padding-right: 0px;
}
.px-0\.5{
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-10{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-\[\.7rem\]{
  padding-left: .7rem;
  padding-right: .7rem;
}
.px-\[\.85rem\]{
  padding-left: .85rem;
  padding-right: .85rem;
}
.px-\[0\.8em\]{
  padding-left: 0.8em;
  padding-right: 0.8em;
}
.px-\[15px\]{
  padding-left: 15px;
  padding-right: 15px;
}
.px-\[23px\]{
  padding-left: 23px;
  padding-right: 23px;
}
.px-\[25px\]{
  padding-left: 25px;
  padding-right: 25px;
}
.px-\[50px\]{
  padding-left: 50px;
  padding-right: 50px;
}
.px-\[7\.5px\]{
  padding-left: 7.5px;
  padding-right: 7.5px;
}
.py-0{
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5{
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-\[\.375rem\]{
  padding-top: .375rem;
  padding-bottom: .375rem;
}
.py-\[\.775rem\]{
  padding-top: .775rem;
  padding-bottom: .775rem;
}
.py-\[\.8rem\]{
  padding-top: .8rem;
  padding-bottom: .8rem;
}
.py-\[0\.45em\]{
  padding-top: 0.45em;
  padding-bottom: 0.45em;
}
.py-\[100px\]{
  padding-top: 100px;
  padding-bottom: 100px;
}
.py-\[13px\]{
  padding-top: 13px;
  padding-bottom: 13px;
}
.py-\[15px\]{
  padding-top: 15px;
  padding-bottom: 15px;
}
.py-\[60px\]{
  padding-top: 60px;
  padding-bottom: 60px;
}
.py-\[90px\]{
  padding-top: 90px;
  padding-bottom: 90px;
}
.py-\[calc\(\.375rem_\+_1px\)\]{
  padding-top: calc(.375rem + 1px);
  padding-bottom: calc(.375rem + 1px);
}
.py-\[calc\(\.775rem_\+_1px\)\]{
  padding-top: calc(.775rem + 1px);
  padding-bottom: calc(.775rem + 1px);
}
.py-\[calc\(\.8rem_\+_1px\)\]{
  padding-top: calc(.8rem + 1px);
  padding-bottom: calc(.8rem + 1px);
}
.\!pb-0{
  padding-bottom: 0px !important;
}
.\!pr-0{
  padding-right: 0px !important;
}
.\!pt-0{
  padding-top: 0px !important;
}
.\!pt-2{
  padding-top: 0.5rem !important;
}
.\!pt-3{
  padding-top: 0.75rem !important;
}
.pb-0{
  padding-bottom: 0px;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-5{
  padding-bottom: 1.25rem;
}
.pb-\[3\.75px\]{
  padding-bottom: 3.75px;
}
.pb-\[50px\]{
  padding-bottom: 50px;
}
.pe-2{
  padding-inline-end: 0.5rem;
}
.pl-0{
  padding-left: 0px;
}
.pl-0\.5{
  padding-left: 0.125rem;
}
.pl-1{
  padding-left: 0.25rem;
}
.pl-10{
  padding-left: 2.5rem;
}
.pl-11{
  padding-left: 2.75rem;
}
.pl-3{
  padding-left: 0.75rem;
}
.pl-4{
  padding-left: 1rem;
}
.pl-9{
  padding-left: 2.25rem;
}
.pl-\[\.7rem\]{
  padding-left: .7rem;
}
.pl-\[30px\]{
  padding-left: 30px;
}
.pr-0{
  padding-right: 0px;
}
.pr-10{
  padding-right: 2.5rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pr-24{
  padding-right: 6rem;
}
.pr-3{
  padding-right: 0.75rem;
}
.pr-4{
  padding-right: 1rem;
}
.pr-5{
  padding-right: 1.25rem;
}
.pr-8{
  padding-right: 2rem;
}
.pr-\[30px\]{
  padding-right: 30px;
}
.ps-0{
  padding-inline-start: 0px;
}
.pt-0{
  padding-top: 0px;
}
.pt-10{
  padding-top: 2.5rem;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-20{
  padding-top: 5rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-5{
  padding-top: 1.25rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pt-\[100px\]{
  padding-top: 100px;
}
.pt-\[110px\]{
  padding-top: 110px;
}
.pt-\[140px\]{
  padding-top: 140px;
}
.pt-\[7\.5px\]{
  padding-top: 7.5px;
}
.text-left{
  text-align: left;
}
.\!text-center{
  text-align: center !important;
}
.text-center{
  text-align: center;
}
.\!text-right{
  text-align: right !important;
}
.text-right{
  text-align: right;
}
.text-start{
  text-align: start;
}
.text-end{
  text-align: end;
}
.indent-8{
  text-indent: 2rem;
}
.indent-\[-9999px\]{
  text-indent: -9999px;
}
.align-baseline{
  vertical-align: baseline;
}
.align-top{
  vertical-align: top;
}
.\!align-middle{
  vertical-align: middle !important;
}
.align-middle{
  vertical-align: middle;
}
.align-bottom{
  vertical-align: bottom;
}
.align-text-top{
  vertical-align: text-top;
}
.align-text-bottom{
  vertical-align: text-bottom;
}
.align-sub{
  vertical-align: sub;
}
.\!text-base{
  font-size: 0.875rem !important;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl{
  font-size: 3rem;
  line-height: 1;
}
.text-\[\.75em\]{
  font-size: .75em;
}
.text-\[\.765625rem\]{
  font-size: .765625rem;
}
.text-\[1\.09375rem\]{
  font-size: 1.09375rem;
}
.text-\[10px\]{
  font-size: 10px;
}
.text-\[11px\]{
  font-size: 11px;
}
.text-\[12px\]{
  font-size: 12px;
}
.text-\[13px\]{
  font-size: 13px;
}
.text-\[14px\]{
  font-size: 14px;
}
.text-\[16px\]{
  font-size: 16px;
}
.text-\[17px\]{
  font-size: 17px;
}
.text-\[18px\]{
  font-size: 18px;
}
.text-\[1rem\]{
  font-size: 1rem;
}
.text-\[20px\]{
  font-size: 20px;
}
.text-\[22px\]{
  font-size: 22px;
}
.text-\[24px\]{
  font-size: 24px;
}
.text-\[26px\]{
  font-size: 26px;
}
.text-\[28px\]{
  font-size: 28px;
}
.text-\[40px\]{
  font-size: 40px;
}
.text-\[46px\]{
  font-size: 46px;
}
.text-\[50px\]{
  font-size: 50px;
}
.text-\[80\%\]{
  font-size: 80%;
}
.text-base{
  font-size: 0.875rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.75rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.lowercase{
  text-transform: lowercase;
}
.capitalize{
  text-transform: capitalize;
}
.italic{
  font-style: italic;
}
.leading-\[0\.9\]{
  line-height: 0.9;
}
.leading-\[1\.1\]{
  line-height: 1.1;
}
.leading-\[1\.2\]{
  line-height: 1.2;
}
.leading-\[30px\]{
  line-height: 30px;
}
.leading-\[60px\]{
  line-height: 60px;
}
.leading-none{
  line-height: 1;
}
.leading-normal{
  line-height: 1.5;
}
.leading-snug{
  line-height: 1.375;
}
.leading-tight{
  line-height: 1.25;
}
.\!text-danger-500{
  --tw-text-opacity: 1 !important;
  color: rgb(220 38 38 / var(--tw-text-opacity)) !important;
}
.\!text-dark-500{
  --tw-text-opacity: 1 !important;
  color: rgb(33 37 41 / var(--tw-text-opacity)) !important;
}
.\!text-info-500{
  --tw-text-opacity: 1 !important;
  color: rgb(62 201 214 / var(--tw-text-opacity)) !important;
}
.\!text-purple-500{
  --tw-text-opacity: 1 !important;
  color: rgb(168 85 247 / var(--tw-text-opacity)) !important;
}
.\!text-success-500{
  --tw-text-opacity: 1 !important;
  color: rgb(44 168 127 / var(--tw-text-opacity)) !important;
}
.\!text-theme-bodycolor{
  --tw-text-opacity: 1 !important;
  color: rgb(19 25 32 / var(--tw-text-opacity)) !important;
}
.\!text-themedark-bodycolor{
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
.\!text-warning-500{
  --tw-text-opacity: 1 !important;
  color: rgb(229 138 0 / var(--tw-text-opacity)) !important;
}
.\!text-white{
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}
.text-\[\#0077b5\]{
  --tw-text-opacity: 1;
  color: rgb(0 119 181 / var(--tw-text-opacity));
}
.text-\[\#4267b2\]{
  --tw-text-opacity: 1;
  color: rgb(66 103 178 / var(--tw-text-opacity));
}
.text-\[\#42c0fb\]{
  --tw-text-opacity: 1;
  color: rgb(66 192 251 / var(--tw-text-opacity));
}
.text-\[\#999999\]{
  --tw-text-opacity: 1;
  color: rgb(153 153 153 / var(--tw-text-opacity));
}
.text-blue-200{
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}
.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}
.text-danger{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.text-danger-400{
  --tw-text-opacity: 1;
  color: rgb(225 71 71 / var(--tw-text-opacity));
}
.text-danger-500{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.text-danger-800{
  --tw-text-opacity: 1;
  color: rgb(206 23 23 / var(--tw-text-opacity));
}
.text-dark{
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.text-dark-200{
  --tw-text-opacity: 1;
  color: rgb(158 167 177 / var(--tw-text-opacity));
}
.text-dark-500{
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.text-dark-800{
  --tw-text-opacity: 1;
  color: rgb(13 14 16 / var(--tw-text-opacity));
}
.text-gray-200{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
.text-green-200{
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity));
}
.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}
.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}
.text-info-500{
  --tw-text-opacity: 1;
  color: rgb(62 201 214 / var(--tw-text-opacity));
}
.text-info-800{
  --tw-text-opacity: 1;
  color: rgb(40 181 198 / var(--tw-text-opacity));
}
.text-inherit{
  color: inherit;
}
.text-primary{
  color: rgb(var(--colors-primary));
}
.text-primary-500{
  color: rgb(var(--colors-primary-500));
}
.text-primary-500\/30{
  color: rgb(var(--colors-primary-500) / 0.3);
}
.text-primary-800{
  color: rgb(var(--colors-primary-800));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}
.text-secondary{
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.text-secondary-200{
  --tw-text-opacity: 1;
  color: rgb(190 198 206 / var(--tw-text-opacity));
}
.text-secondary-500{
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.text-secondary-800{
  --tw-text-opacity: 1;
  color: rgb(38 45 51 / var(--tw-text-opacity));
}
.text-slate-500{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.text-slate-900{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.text-success{
  --tw-text-opacity: 1;
  color: rgb(44 168 127 / var(--tw-text-opacity));
}
.text-success-500{
  --tw-text-opacity: 1;
  color: rgb(44 168 127 / var(--tw-text-opacity));
}
.text-success-800{
  --tw-text-opacity: 1;
  color: rgb(27 141 98 / var(--tw-text-opacity));
}
.text-theme-bodycolor{
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.text-theme-bodycolor\/50{
  color: rgb(19 25 32 / 0.5);
}
.text-theme-bodycolor\/70{
  color: rgb(19 25 32 / 0.7);
}
.text-theme-headercolor{
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.text-theme-headings{
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.text-theme-horizontalsubmenucolor{
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.text-theme-secondarytextcolor{
  color: rgba(33, 37, 41, 0.75);
}
.text-theme-sidebarcolor{
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.text-themedark-bodycolor{
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.text-themedark-bodycolor\/50{
  color: rgb(191 191 191 / 0.5);
}
.text-themedark-bodycolor\/70{
  color: rgb(191 191 191 / 0.7);
}
.text-themedark-headings{
  color: rgba(255, 255, 255, 0.8);
}
.text-themedark-secondarytextcolor{
  --tw-text-opacity: 1;
  color: rgb(116 136 146 / var(--tw-text-opacity));
}
.text-themedark-sidebarcolor{
  color: rgba(255, 255, 255, 0.5);
}
.text-transparent{
  color: transparent;
}
.text-warning{
  --tw-text-opacity: 1;
  color: rgb(229 138 0 / var(--tw-text-opacity));
}
.text-warning-500{
  --tw-text-opacity: 1;
  color: rgb(229 138 0 / var(--tw-text-opacity));
}
.text-warning-800{
  --tw-text-opacity: 1;
  color: rgb(218 109 0 / var(--tw-text-opacity));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-yellow-200{
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity));
}
.text-yellow-400{
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}
.text-yellow-600{
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity));
}
.text-opacity-50{
  --tw-text-opacity: 0.5;
}
.text-opacity-75{
  --tw-text-opacity: 0.75;
}
.underline{
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.line-through{
  -webkit-text-decoration-line: line-through;
          text-decoration-line: line-through;
}
.no-underline{
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-15{
  opacity: 0.15;
}
.opacity-20{
  opacity: 0.2;
}
.opacity-25{
  opacity: 0.25;
}
.opacity-40{
  opacity: 0.4;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-70{
  opacity: 0.7;
}
.opacity-75{
  opacity: 0.75;
}
.opacity-80{
  opacity: 0.8;
}
.opacity-90{
  opacity: 0.9;
}
.\!shadow-none{
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[-6px_0px_14px_1px_rgba\(27\2c 46\2c 94\2c 0\.04\)\]{
  --tw-shadow: -6px 0px 14px 1px rgba(27,46,94,0.04);
  --tw-shadow-colored: -6px 0px 14px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_0_2px_\#fff\]{
  --tw-shadow: 0 0 0 2px #fff;
  --tw-shadow-colored: 0 0 0 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_0_3px_\#fff\]{
  --tw-shadow: 0 0 0 3px #fff;
  --tw-shadow-colored: 0 0 0 3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_15px_-3px_rgb\(0\2c 0\2c 0\2c 0\.1\)\]{
  --tw-shadow: 0 0 15px -3px rgb(0,0,0,0.1);
  --tw-shadow-colored: 0 0 15px -3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_24px_rgba\(27\2c 46\2c 94\2c \.05\)\]{
  --tw-shadow: 0 0 24px rgba(27,46,94,.05);
  --tw-shadow-colored: 0 0 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_24px_0_rgba\(62\2c 57\2c 107\2c \.18\)\]{
  --tw-shadow: 0 4px 24px 0 rgba(62,57,107,.18);
  --tw-shadow-colored: 0 4px 24px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_24px_0_rgba\(62\2c 57\2c 107\2c 0\.18\)\]{
  --tw-shadow: 0 4px 24px 0 rgba(62,57,107,0.18);
  --tw-shadow-colored: 0 4px 24px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_6px_12px_rgba\(0\2c 0\2c 0\2c \.17\)\]{
  --tw-shadow: 0 6px 12px rgba(0,0,0,.17);
  --tw-shadow-colored: 0 6px 12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_6px_7px_-1px_rgba\(80\2c 86\2c 175\2c \.3\)\]{
  --tw-shadow: 0 6px 7px -1px rgba(80,86,175,.3);
  --tw-shadow-colored: 0 6px 7px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_8px_24px_rgba\(27\2c 46\2c 94\2c 0\.08\)\]{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.08);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_8px_24px_rgba\(27\2c 46\2c 94\2c 0\.12\)\]{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.12);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[1px_0_3px_0_rgba\(219\2c 224\2c 229\2c 1\)\]{
  --tw-shadow: 1px 0 3px 0 rgba(219,224,229,1);
  --tw-shadow-colored: 1px 0 3px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[inset_0_0_1px_1px_\#fff\]{
  --tw-shadow: inset 0 0 1px 1px #fff;
  --tw-shadow-colored: inset 0 0 1px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-theme-border{
  --tw-shadow-color: #e7eaee;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-theme-cardbg{
  --tw-shadow-color: #fff;
  --tw-shadow: var(--tw-shadow-colored);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.\!outline-themedark-border{
  outline-color: #303f50 !important;
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale{
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur{
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-\[10px\]{
  --tw-backdrop-blur: blur(10px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-\[3px\]{
  --tw-backdrop-blur: blur(3px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-\[7px\]{
  --tw-backdrop-blur: blur(7px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xl{
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[all_0\.3s_cubic-bezier\(0\.25\2c 0\.5\2c 0\.5\2c 0\.9\)\]{
  transition-property: all 0.3s cubic-bezier(0.25,0.5,0.5,0.9);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[transform_0\.2s_linear\]{
  transition-property: transform 0.2s linear;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-500{
  transition-duration: 500ms;
}
.ease-\[cubic-bezier\(0\.455\2c 0\.03\2c 0\.515\2c 0\.955\)\]{
  transition-timing-function: cubic-bezier(0.455,0.03,0.515,0.955);
}
.ease-in{
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear{
  transition-timing-function: linear;
}
.content-\[\"\"\]{
  --tw-content: "";
  content: var(--tw-content);
}
.pc-header .dropdown-menu.dropdown-notification .\*\:card > *{
  cursor: pointer;
}
.pc-header .dropdown-menu.dropdown-notification .\*\:card > *:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 245 247 / var(--tw-bg-opacity));
}
.pc-header .dropdown-menu.dropdown-notification .\*\:card > *:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(25 33 42 / var(--tw-bg-opacity));
}
[data-pc-theme_contrast="true"] .\*\:card > *{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.08);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 1025px){
  [data-pc-layout="horizontal"] .pc-sidebar .\*\:card > *{
    display: none;
  }
}
.\*\:card > *{
  position: relative;
  margin-bottom: 1.5rem;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.\*\:card > *:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.\*\:card > * .card-header{
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  padding: 25px;
}
.\*\:card > * .card-header:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.\*\:card > * .card-body{
  padding: 25px;
}
.\*\:card > * .card-footer{
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
  padding: 25px;
}
.\*\:card > * .card-footer:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.\*\:card > * .card-link{
  margin-right: 0.75rem;
  display: inline-block;
  color: rgb(var(--colors-primary-500));
}
.\*\:card > * .card-link:hover{
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.\*\:card > * .card-link:hover:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
@media (min-width: 640px){
  .dt-container>div.sm\:mt-2.row{
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
}
.\*\:relative > *{
  position: relative;
}
.\*\:m-3 > *{
  margin: 0.75rem;
}
.\*\:m-\[5px\] > *{
  margin: 5px;
}
.\*\:\*\:mx-2\.5 > * > *{
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}
.\*\:\*\:my-0\.5 > * > *{
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.\*\:\*\:my-2\.5 > * > *{
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}
.\*\:\*\:my-px > * > *{
  margin-top: 1px;
  margin-bottom: 1px;
}
.\*\:mx-2\.5 > *{
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}
.\*\:mx-auto > *{
  margin-left: auto;
  margin-right: auto;
}
.\*\:my-1 > *{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.\*\:my-2 > *{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.\*\:my-2\.5 > *{
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}
.\*\:my-\[14px\] > *{
  margin-top: 14px;
  margin-bottom: 14px;
}
.\*\:mb-2 > *{
  margin-bottom: 0.5rem;
}
.\*\:mb-3 > *{
  margin-bottom: 0.75rem;
}
.\*\:ml-1 > *{
  margin-left: 0.25rem;
}
.\*\:ml-2 > *{
  margin-left: 0.5rem;
}
.\*\:mr-1 > *{
  margin-right: 0.25rem;
}
.\*\:mr-2 > *{
  margin-right: 0.5rem;
}
.\*\:\*\:block > * > *{
  display: block;
}
.\*\:block > *{
  display: block;
}
.\*\:\*\:inline-block > * > *{
  display: inline-block;
}
.\*\:inline-block > *{
  display: inline-block;
}
.\*\:\*\:flex > * > *{
  display: flex;
}
.\*\:flex > *{
  display: flex;
}
.\*\:inline-flex > *{
  display: inline-flex;
}
.\*\:h-10 > *{
  height: 2.5rem;
}
.\*\:h-20 > *{
  height: 5rem;
}
.\*\:h-4 > *{
  height: 1rem;
}
.\*\:h-\[125px\] > *{
  height: 125px;
}
.\*\:h-\[30px\] > *{
  height: 30px;
}
.\*\:h-\[70px\] > *{
  height: 70px;
}
.\*\:min-h-header-height > *{
  min-height: 74px;
}
.\*\:w-10 > *{
  width: 2.5rem;
}
.\*\:w-20 > *{
  width: 5rem;
}
.\*\:w-4 > *{
  width: 1rem;
}
.\*\:w-\[30px\] > *{
  width: 30px;
}
.\*\:w-\[70px\] > *{
  width: 70px;
}
.\*\:w-auto > *{
  width: auto;
}
.\*\:w-full > *{
  width: 100%;
}
.\*\:flex-\[0_0_auto\] > *{
  flex: 0 0 auto;
}
.\*\:shrink-0 > *{
  flex-shrink: 0;
}
.\*\:\*\:cursor-pointer > * > *{
  cursor: pointer;
}
.\*\:cursor-pointer > *{
  cursor: pointer;
}
.\*\:\*\:items-center > * > *{
  align-items: center;
}
.\*\:items-center > *{
  align-items: center;
}
.\*\:justify-center > *{
  justify-content: center;
}
.\*\:\*\:justify-between > * > *{
  justify-content: space-between;
}
.\*\:gap-2\.5 > *{
  gap: 0.625rem;
}
.\*\:gap-3 > *{
  gap: 0.75rem;
}
.\*\:whitespace-nowrap > *{
  white-space: nowrap;
}
.\*\:\*\:rounded-lg > * > *{
  border-radius: 0.5rem;
}
.\*\:\*\:rounded-md > * > *{
  border-radius: 0.375rem;
}
.\*\:\*\:rounded-xl > * > *{
  border-radius: 0.75rem;
}
.\*\:rounded-full > *{
  border-radius: 9999px;
}
.\*\:rounded-lg > *{
  border-radius: 0.5rem;
}
.\*\:rounded-none > *{
  border-radius: 0px;
}
.\*\:rounded-xl > *{
  border-radius: 0.75rem;
}
.\*\:border > *{
  border-width: 1px;
}
.\*\:border-2 > *{
  border-width: 2px;
}
.\*\:border-theme-border > *{
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.\*\:border-white > *{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.\*\:bg-theme-cardbg > *{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.\*\:\*\:p-\[30px\] > * > *{
  padding: 30px;
}
.\*\:\*\:px-2 > * > *{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\*\:\*\:px-3 > * > *{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.\*\:\*\:px-4 > * > *{
  padding-left: 1rem;
  padding-right: 1rem;
}
.\*\:\*\:px-6 > * > *{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.\*\:\*\:py-1 > * > *{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.\*\:\*\:py-1\.5 > * > *{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.\*\:\*\:py-3 > * > *{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.\*\:\*\:py-3\.5 > * > *{
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.\*\:px-0 > *{
  padding-left: 0px;
  padding-right: 0px;
}
.\*\:px-2 > *{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\*\:px-\[25px\] > *{
  padding-left: 25px;
  padding-right: 25px;
}
.\*\:py-2 > *{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.\*\:py-3 > *{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.\*\:py-4 > *{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.\*\:py-\[5px\] > *{
  padding-top: 5px;
  padding-bottom: 5px;
}
.\*\:text-left > *{
  text-align: left;
}
.\*\:text-2xl > *{
  font-size: 1.5rem;
  line-height: 2rem;
}
.\*\:text-theme-bodycolor > *{
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.\*\:\*\:opacity-40 > * > *{
  opacity: 0.4;
}
.\*\:shadow-\[0_0_0_2px_\#fff\] > *{
  --tw-shadow: 0 0 0 2px #fff;
  --tw-shadow-colored: 0 0 0 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.\*\:shadow-\[0px_8px_24px_rgba\(27\2c 46\2c 94\2c 0\.08\)\] > *{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.08);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.\*\:\*\:grayscale > * > *{
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.\*\:\*\:transition-all > * > *{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.\*\:transition-all > *{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.\*\:\*\:duration-300 > * > *{
  transition-duration: 300ms;
}
.\*\:duration-200 > *{
  transition-duration: 200ms;
}
.\*\:\*\:ease-linear > * > *{
  transition-timing-function: linear;
}
.marker\:absolute *::marker{
  position: absolute;
}
.marker\:absolute::marker{
  position: absolute;
}
.placeholder\:text-\[\#bec8d0\]::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.placeholder\:text-\[\#bec8d0\]::placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.before\:absolute::before{
  content: var(--tw-content);
  position: absolute;
}
.before\:left-0::before{
  content: var(--tw-content);
  left: 0px;
}
.before\:left-\[15px\]::before{
  content: var(--tw-content);
  left: 15px;
}
.before\:left-\[30px\]::before{
  content: var(--tw-content);
  left: 30px;
}
.before\:left-\[45px\]::before{
  content: var(--tw-content);
  left: 45px;
}
.before\:right-0::before{
  content: var(--tw-content);
  right: 0px;
}
.before\:top-2\/4::before{
  content: var(--tw-content);
  top: 50%;
}
.before\:\!hidden::before{
  content: var(--tw-content);
  display: none !important;
}
.before\:hidden::before{
  content: var(--tw-content);
  display: none;
}
.before\:h-\[50px\]::before{
  content: var(--tw-content);
  height: 50px;
}
.before\:w-\[50px\]::before{
  content: var(--tw-content);
  width: 50px;
}
.before\:-translate-y-2\/4::before{
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:translate-x-2\/4::before{
  content: var(--tw-content);
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:-rotate-45::before{
  content: var(--tw-content);
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:rotate-180::before{
  content: var(--tw-content);
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:rounded-full::before{
  content: var(--tw-content);
  border-radius: 9999px;
}
.before\:rounded-md::before{
  content: var(--tw-content);
  border-radius: 0.375rem;
}
.before\:border-l::before{
  content: var(--tw-content);
  border-left-width: 1px;
}
.before\:border-t::before{
  content: var(--tw-content);
  border-top-width: 1px;
}
.before\:border-dashed::before{
  content: var(--tw-content);
  border-style: dashed;
}
.before\:border-primary-500::before{
  content: var(--tw-content);
  border-color: rgb(var(--colors-primary-500));
}
.before\:border-warning-500::before{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(229 138 0 / var(--tw-border-opacity));
}
.before\:bg-slate-500::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}
.before\:bg-theme-cardbg::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.before\:font-\[\'tabler-icons\'\]::before{
  content: var(--tw-content);
  font-family: 'tabler-icons';
}
.before\:text-\[28px\]::before{
  content: var(--tw-content);
  font-size: 28px;
}
.before\:text-lg::before{
  content: var(--tw-content);
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.before\:leading-\[15px\]::before{
  content: var(--tw-content);
  line-height: 15px;
}
.before\:text-\[\#726204\]::before{
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(114 98 4 / var(--tw-text-opacity));
}
.before\:opacity-0::before{
  content: var(--tw-content);
  opacity: 0;
}
.before\:transition-all::before{
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.before\:duration-500::before{
  content: var(--tw-content);
  transition-duration: 500ms;
}
.before\:ease-\[cubic-bezier\(\.47\2c 1\.64\2c \.41\2c \.8\)\]::before{
  content: var(--tw-content);
  transition-timing-function: cubic-bezier(.47,1.64,.41,.8);
}
.before\:content-\[\'\'\]::before{
  --tw-content: '';
  content: var(--tw-content);
}
.before\:content-\[\'\\\\2605\'\]::before{
  --tw-content: '\\2605';
  content: var(--tw-content);
}
.before\:content-\[\'\\\\ea5f\'\]::before{
  --tw-content: '\\ea5f';
  content: var(--tw-content);
}
.before\:content-\[\'\2014\'\]::before{
  --tw-content: '—';
  content: var(--tw-content);
}
.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}
.after\:inset-0::after{
  content: var(--tw-content);
  inset: 0px;
}
.after\:inset-\[2px\]::after{
  content: var(--tw-content);
  inset: 2px;
}
.after\:left-0::after{
  content: var(--tw-content);
  left: 0px;
}
.after\:left-\[30px\]::after{
  content: var(--tw-content);
  left: 30px;
}
.after\:left-\[40px\]::after{
  content: var(--tw-content);
  left: 40px;
}
.after\:left-\[52px\]::after{
  content: var(--tw-content);
  left: 52px;
}
.after\:right-\[11\.5px\]::after{
  content: var(--tw-content);
  right: 11.5px;
}
.after\:start-\[2px\]::after{
  content: var(--tw-content);
  inset-inline-start: 2px;
}
.after\:top-2\/4::after{
  content: var(--tw-content);
  top: 50%;
}
.after\:top-5::after{
  content: var(--tw-content);
  top: 1.25rem;
}
.after\:top-\[2px\]::after{
  content: var(--tw-content);
  top: 2px;
}
.after\:z-10::after{
  content: var(--tw-content);
  z-index: 10;
}
.after\:z-\[1\]::after{
  content: var(--tw-content);
  z-index: 1;
}
.after\:\!hidden::after{
  content: var(--tw-content);
  display: none !important;
}
.after\:hidden::after{
  content: var(--tw-content);
  display: none;
}
.after\:h-0::after{
  content: var(--tw-content);
  height: 0px;
}
.after\:h-5::after{
  content: var(--tw-content);
  height: 1.25rem;
}
.after\:h-\[50px\]::after{
  content: var(--tw-content);
  height: 50px;
}
.after\:h-\[5px\]::after{
  content: var(--tw-content);
  height: 5px;
}
.after\:w-0::after{
  content: var(--tw-content);
  width: 0px;
}
.after\:w-5::after{
  content: var(--tw-content);
  width: 1.25rem;
}
.after\:w-\[50px\]::after{
  content: var(--tw-content);
  width: 50px;
}
.after\:w-\[5px\]::after{
  content: var(--tw-content);
  width: 5px;
}
.after\:-translate-x-2\/4::after{
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\:-translate-y-2\/4::after{
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\:-rotate-45::after{
  content: var(--tw-content);
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\:scale-0::after{
  content: var(--tw-content);
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\:scale-\[1\.2\]::after{
  content: var(--tw-content);
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\:rounded-\[8px\]::after{
  content: var(--tw-content);
  border-radius: 8px;
}
.after\:rounded-full::after{
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:border::after{
  content: var(--tw-content);
  border-width: 1px;
}
.after\:border-\[5px\]::after{
  content: var(--tw-content);
  border-width: 5px;
}
.after\:border-b::after{
  content: var(--tw-content);
  border-bottom-width: 1px;
}
.after\:border-r::after{
  content: var(--tw-content);
  border-right-width: 1px;
}
.after\:border-dashed::after{
  content: var(--tw-content);
  border-style: dashed;
}
.after\:border-\[\#131920_transparent_transparent\]::after{
  content: var(--tw-content);
  border-color: #131920 transparent transparent;
}
.after\:border-gray-300::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.after\:border-primary-500::after{
  content: var(--tw-content);
  border-color: rgb(var(--colors-primary-500));
}
.after\:border-warning-500::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(229 138 0 / var(--tw-border-opacity));
}
.after\:bg-\[rgba\(255\2c 255\2c 255\2c 0\.15\)\]::after{
  content: var(--tw-content);
  background-color: rgba(255,255,255,0.15);
}
.after\:bg-primary-500::after{
  content: var(--tw-content);
  background-color: rgb(var(--colors-primary-500));
}
.after\:bg-theme-activebg::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(243 245 247 / var(--tw-bg-opacity));
}
.after\:bg-theme-cardbg::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.after\:bg-theme-sidebarcolor::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.after\:bg-white::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.after\:bg-\[linear-gradient\(213\.66deg\2c transparent_25\.46\%\2c rgba\(0\2c 0\2c 0\2c 0\.2\)_68\.77\%\2c rgba\(0\2c 0\2c 0\2c 0\.3\)_81\.72\%\)\]::after{
  content: var(--tw-content);
  background-image: linear-gradient(213.66deg,transparent 25.46%,rgba(0,0,0,0.2) 68.77%,rgba(0,0,0,0.3) 81.72%);
}
.after\:align-bottom::after{
  content: var(--tw-content);
  vertical-align: bottom;
}
.after\:font-\[\'tabler-icons\'\]::after{
  content: var(--tw-content);
  font-family: 'tabler-icons';
}
.after\:text-base::after{
  content: var(--tw-content);
  font-size: 0.875rem;
}
.after\:opacity-10::after{
  content: var(--tw-content);
  opacity: 0.1;
}
.after\:opacity-100::after{
  content: var(--tw-content);
  opacity: 1;
}
.after\:opacity-50::after{
  content: var(--tw-content);
  opacity: 0.5;
}
.after\:transition::after{
  content: var(--tw-content);
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:transition-all::after{
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:\!content-\[\"\"\]::after{
  --tw-content: "" !important;
  content: var(--tw-content) !important;
}
.after\:content-\[\"\"\]::after{
  --tw-content: "";
  content: var(--tw-content);
}
.after\:content-\[\'\'\]::after{
  --tw-content: '';
  content: var(--tw-content);
}
.after\:content-\[\'\\\\ea5f\'\]::after{
  --tw-content: '\\ea5f';
  content: var(--tw-content);
}
.first\:pt-2\.5:first-child{
  padding-top: 0.625rem;
}
.\*\:first\:ml-0:first-child > *{
  margin-left: 0px;
}
.first\:\*\:rounded-l-lg > *:first-child{
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.first\:\*\:pt-0 > *:first-child{
  padding-top: 0px;
}
.last\:opacity-75:last-child{
  opacity: 0.75;
}
.last\:\*\:rounded-r-lg > *:last-child{
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.last\:\*\:pb-0 > *:last-child{
  padding-bottom: 0px;
}
.odd\:bg-secondary-300\/10:nth-child(odd){
  background-color: rgb(155 168 180 / 0.1);
}
.open\:left-0[open]{
  left: 0px;
}
.open\:z-\[1030\][open]{
  z-index: 1030;
}
.open\:w-\[290px\][open]{
  width: 290px;
}
.open\:\*\:\*\:\!bg-primary-500\/10 > * > *[open]{
  background-color: rgb(var(--colors-primary-500) / 0.1) !important;
}
.open\:\*\:\*\:text-primary-500 > * > *[open]{
  color: rgb(var(--colors-primary-500));
}
.checked\:\!border-danger-500:checked{
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 38 38 / var(--tw-border-opacity)) !important;
}
.checked\:\!border-dark-500:checked{
  --tw-border-opacity: 1 !important;
  border-color: rgb(33 37 41 / var(--tw-border-opacity)) !important;
}
.checked\:\!border-info-500:checked{
  --tw-border-opacity: 1 !important;
  border-color: rgb(62 201 214 / var(--tw-border-opacity)) !important;
}
.checked\:\!border-primary-500:checked{
  border-color: rgb(var(--colors-primary-500)) !important;
}
.checked\:\!border-secondary-500:checked{
  --tw-border-opacity: 1 !important;
  border-color: rgb(91 107 121 / var(--tw-border-opacity)) !important;
}
.checked\:\!border-success-500:checked{
  --tw-border-opacity: 1 !important;
  border-color: rgb(44 168 127 / var(--tw-border-opacity)) !important;
}
.checked\:\!border-warning-500:checked{
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 138 0 / var(--tw-border-opacity)) !important;
}
.checked\:border-primary-500:checked{
  border-color: rgb(var(--colors-primary-500));
}
.checked\:\!bg-danger-500:checked{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity)) !important;
}
.checked\:\!bg-dark-500:checked{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity)) !important;
}
.checked\:\!bg-info-500:checked{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(62 201 214 / var(--tw-bg-opacity)) !important;
}
.checked\:\!bg-primary-500:checked{
  background-color: rgb(var(--colors-primary-500)) !important;
}
.checked\:\!bg-secondary-500:checked{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity)) !important;
}
.checked\:\!bg-success-500:checked{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity)) !important;
}
.checked\:\!bg-warning-500:checked{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity)) !important;
}
.checked\:bg-primary-500:checked{
  background-color: rgb(var(--colors-primary-500));
}
.checked\:bg-checkbox-bg:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.checked\:bg-radio-bg:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e");
}
.checked\:bg-switch-active-bg:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
}
.checked\:bg-right:checked{
  background-position: right;
}
.hover\:border-primary-500:hover{
  border-color: rgb(var(--colors-primary-500));
}
.hover\:border-theme-border:hover{
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.hover\:\!bg-\[rgba\(0\2c 0\2c 0\2c \.04\)\]:hover{
  background-color: rgba(0,0,0,.04) !important;
}
.hover\:bg-\[\#0077b5\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(0 119 181 / var(--tw-bg-opacity));
}
.hover\:bg-\[\#4267b2\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(66 103 178 / var(--tw-bg-opacity));
}
.hover\:bg-\[\#42c0fb\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(66 192 251 / var(--tw-bg-opacity));
}
.hover\:bg-\[rgba\(0\2c 0\2c 0\2c \.1\)\]:hover{
  background-color: rgba(0,0,0,.1);
}
.hover\:bg-danger-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(245 190 190 / var(--tw-bg-opacity));
}
.hover\:bg-danger-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
.hover\:bg-danger-500\/10:hover{
  background-color: rgb(220 38 38 / 0.1);
}
.hover\:bg-danger-500\/20:hover{
  background-color: rgb(220 38 38 / 0.2);
}
.hover\:bg-danger-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(216 34 34 / var(--tw-bg-opacity));
}
.hover\:bg-dark-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(206 210 215 / var(--tw-bg-opacity));
}
.hover\:bg-dark-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.hover\:bg-dark-500\/20:hover{
  background-color: rgb(33 37 41 / 0.2);
}
.hover\:bg-dark-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity));
}
.hover\:bg-gray-900:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}
.hover\:bg-info-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(197 239 243 / var(--tw-bg-opacity));
}
.hover\:bg-info-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(62 201 214 / var(--tw-bg-opacity));
}
.hover\:bg-info-500\/20:hover{
  background-color: rgb(62 201 214 / 0.2);
}
.hover\:bg-info-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(56 195 209 / var(--tw-bg-opacity));
}
.hover\:bg-inherit:hover{
  background-color: inherit;
}
.hover\:bg-primary-100:hover{
  background-color: rgb(var(--colors-primary-100));
}
.hover\:bg-primary-500:hover{
  background-color: rgb(var(--colors-primary-500));
}
.hover\:bg-primary-500\/10:hover{
  background-color: rgb(var(--colors-primary-500) / 0.1);
}
.hover\:bg-primary-500\/20:hover{
  background-color: rgb(var(--colors-primary-500) / 0.2);
}
.hover\:bg-primary-600:hover{
  background-color: rgb(var(--colors-primary-600));
}
.hover\:bg-secondary-100\/20:hover{
  background-color: rgb(225 229 232 / 0.2);
}
.hover\:bg-secondary-100\/50:hover{
  background-color: rgb(225 229 232 / 0.5);
}
.hover\:bg-secondary-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(190 198 206 / var(--tw-bg-opacity));
}
.hover\:bg-secondary-300\/10:hover{
  background-color: rgb(155 168 180 / 0.1);
}
.hover\:bg-secondary-50\/20:hover{
  background-color: rgb(242 244 245 / 0.2);
}
.hover\:bg-secondary-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.hover\:bg-secondary-500\/20:hover{
  background-color: rgb(91 107 121 / 0.2);
}
.hover\:bg-secondary-500\/30:hover{
  background-color: rgb(91 107 121 / 0.3);
}
.hover\:bg-secondary-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
}
.hover\:bg-success-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(192 229 217 / var(--tw-bg-opacity));
}
.hover\:bg-success-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
}
.hover\:bg-success-500\/20:hover{
  background-color: rgb(44 168 127 / 0.2);
}
.hover\:bg-success-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(39 160 119 / var(--tw-bg-opacity));
}
.hover\:bg-theme-activebg:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 245 247 / var(--tw-bg-opacity));
}
.hover\:bg-theme-sidebarcolor\/20:hover{
  background-color: rgb(91 107 121 / 0.2);
}
.hover\:bg-transparent:hover{
  background-color: transparent;
}
.hover\:bg-warning-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(247 220 179 / var(--tw-bg-opacity));
}
.hover\:bg-warning-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity));
}
.hover\:bg-warning-500\/20:hover{
  background-color: rgb(229 138 0 / 0.2);
}
.hover\:bg-warning-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(226 130 0 / var(--tw-bg-opacity));
}
.hover\:text-danger-500:hover{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.hover\:text-danger-600:hover{
  --tw-text-opacity: 1;
  color: rgb(216 34 34 / var(--tw-text-opacity));
}
.hover\:text-primary-500:hover{
  color: rgb(var(--colors-primary-500));
}
.hover\:text-primary-500\/60:hover{
  color: rgb(var(--colors-primary-500) / 0.6);
}
.hover\:text-theme-bodycolor:hover{
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.hover\:text-opacity-100:hover{
  --tw-text-opacity: 1;
}
.hover\:underline:hover{
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.hover\:no-underline:hover{
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}
.hover\:opacity-100:hover{
  opacity: 1;
}
.hover\:shadow-\[0_8px_24px_rgba\(27\2c 46\2c 94\2c \.12\)\]:hover{
  --tw-shadow: 0 8px 24px rgba(27,46,94,.12);
  --tw-shadow-colored: 0 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0px_8px_24px_rgba\(27\2c 46\2c 94\2c 0\.08\)\]:hover{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.08);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:\*\:z-10 > *:hover{
  z-index: 10;
}
.hover\:\*\:\*\:bg-secondary-500\/10 > * > *:hover{
  background-color: rgb(91 107 121 / 0.1);
}
.hover\:\*\:\*\:bg-white\/10 > * > *:hover{
  background-color: rgb(255 255 255 / 0.1);
}
.hover\:\*\:bg-secondary-300\/10 > *:hover{
  background-color: rgb(155 168 180 / 0.1);
}
.hover\:\*\:text-primary-500 > *:hover{
  color: rgb(var(--colors-primary-500));
}
.hover\:\*\:\*\:opacity-100 > * > *:hover{
  opacity: 1;
}
.hover\:\*\:\*\:shadow-\[0px_8px_24px_rgba\(27\2c 46\2c 94\2c 0\.08\)\] > * > *:hover{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.08);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:\*\:\*\:grayscale-0 > * > *:hover{
  --tw-grayscale: grayscale(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.hover\:after\:scale-100:hover::after{
  content: var(--tw-content);
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:after\:scale-\[1\.2\]:hover::after{
  content: var(--tw-content);
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:after\:rounded:hover::after{
  content: var(--tw-content);
  border-radius: 0.25rem;
}
.hover\:after\:bg-primary-500:hover::after{
  content: var(--tw-content);
  background-color: rgb(var(--colors-primary-500));
}
.hover\:after\:bg-theme-sidebarcolor:hover::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.hover\:after\:opacity-100:hover::after{
  content: var(--tw-content);
  opacity: 1;
}
.focus\:border-primary-500:focus{
  border-color: rgb(var(--colors-primary-500));
}
.focus\:bg-danger-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(238 147 147 / var(--tw-bg-opacity));
}
.focus\:bg-danger-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(216 34 34 / var(--tw-bg-opacity));
}
.focus\:bg-dark-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(158 167 177 / var(--tw-bg-opacity));
}
.focus\:bg-dark-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity));
}
.focus\:bg-info-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(159 228 235 / var(--tw-bg-opacity));
}
.focus\:bg-info-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(56 195 209 / var(--tw-bg-opacity));
}
.focus\:bg-primary-100\/50:focus{
  background-color: rgb(var(--colors-primary-100) / 0.5);
}
.focus\:bg-primary-50\/20:focus{
  background-color: rgb(var(--colors-primary-50) / 0.2);
}
.focus\:bg-primary-600:focus{
  background-color: rgb(var(--colors-primary-600));
}
.focus\:bg-secondary-200\/50:focus{
  background-color: rgb(190 198 206 / 0.5);
}
.focus\:bg-secondary-50\/20:focus{
  background-color: rgb(242 244 245 / 0.2);
}
.focus\:bg-secondary-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(73 86 98 / var(--tw-bg-opacity));
}
.focus\:bg-success-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(150 212 191 / var(--tw-bg-opacity));
}
.focus\:bg-success-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(39 160 119 / var(--tw-bg-opacity));
}
.focus\:bg-transparent:focus{
  background-color: transparent;
}
.focus\:bg-warning-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(242 197 128 / var(--tw-bg-opacity));
}
.focus\:bg-warning-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(226 130 0 / var(--tw-bg-opacity));
}
.focus\:text-danger-500:focus{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.focus\:text-danger-600:focus{
  --tw-text-opacity: 1;
  color: rgb(216 34 34 / var(--tw-text-opacity));
}
.focus\:text-primary-500:focus{
  color: rgb(var(--colors-primary-500));
}
.focus\:text-white:focus{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.focus\:\!shadow-none:focus{
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.focus\:shadow-none:focus{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:outline-0:focus{
  outline-width: 0px;
}
.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-white:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}
.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}
.focus\:ring-offset-gray-800:focus{
  --tw-ring-offset-color: #1f2937;
}
.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.active\:bg-danger-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(211 28 28 / var(--tw-bg-opacity));
}
.active\:bg-dark-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(19 22 24 / var(--tw-bg-opacity));
}
.active\:bg-info-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(48 188 204 / var(--tw-bg-opacity));
}
.active\:bg-primary-50\/30:active{
  background-color: rgb(var(--colors-primary-50) / 0.3);
}
.active\:bg-primary-700:active{
  background-color: rgb(var(--colors-primary-700));
}
.active\:bg-secondary-50\/30:active{
  background-color: rgb(242 244 245 / 0.3);
}
.active\:bg-secondary-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(56 66 74 / var(--tw-bg-opacity));
}
.active\:bg-success-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(33 151 108 / var(--tw-bg-opacity));
}
.active\:bg-warning-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(222 119 0 / var(--tw-bg-opacity));
}
.active\:text-danger-600:active{
  --tw-text-opacity: 1;
  color: rgb(216 34 34 / var(--tw-text-opacity));
}
.active\:text-primary-500:active{
  color: rgb(var(--colors-primary-500));
}
.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}
.disabled\:bg-secondary-200\/10:disabled{
  background-color: rgb(190 198 206 / 0.1);
}
.disabled\:opacity-50:disabled{
  opacity: 0.5;
}
.group\/mail:hover .group-hover\/mail\:translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/mail:hover .group-hover\/mail\:scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-\[1\.2\]{
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:bg-primary-500{
  background-color: rgb(var(--colors-primary-500));
}
.group:hover .group-hover\:bg-primary-500\/10{
  background-color: rgb(var(--colors-primary-500) / 0.1);
}
.group\/mail:hover .group-hover\/mail\:opacity-100{
  opacity: 1;
}
.group:hover .group-hover\:opacity-100{
  opacity: 1;
}
.group:hover .group-hover\:backdrop-blur-\[10px\]{
  --tw-backdrop-blur: blur(10px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.group.active .group-\[\.active\]\:block{
  display: block;
}
.group.mail-detail-view .group-\[\.mail-detail-view\]\:block{
  display: block;
}
.group.active .group-\[\.active\]\:hidden{
  display: none;
}
.group.mail-detail-view .group-\[\.mail-detail-view\]\:hidden{
  display: none;
}
.group.md-view .group-\[\.md-view\]\:hidden{
  display: none;
}
.group.sm-view .group-\[\.sm-view\]\:hidden{
  display: none;
}
.group.mini-mail-list .group-\[\.mini-mail-list\]\:h-7{
  height: 1.75rem;
}
.group.mini-mail-list .group-\[\.mini-mail-list\]\:h-\[26px\]{
  height: 26px;
}
.group.show .group-\[\.show\]\:h-\[calc\(100vh_-_410px\)\]{
  height: calc(100vh - 410px);
}
.group.show .group-\[\.show\]\:h-\[calc\(100vh_-_415px\)\]{
  height: calc(100vh - 415px);
}
.group.show .group-\[\.show\]\:h-\[calc\(100vh_-_82px\)\]{
  height: calc(100vh - 82px);
}
.group.mini-mail-list .group-\[\.mini-mail-list\]\:w-7{
  width: 1.75rem;
}
.group.mini-mail-list .group-\[\.mini-mail-list\]\:w-\[26px\]{
  width: 26px;
}
.group.mini-mail-list .group-\[\.mini-mail-list\]\:w-\[30px\]{
  width: 30px;
}
.group.tns-nav-active .group-\[\.tns-nav-active\]\:w-\[30px\]{
  width: 30px;
}
.group.modal-pos-down .group-\[\.modal-pos-down\]\:max-w-\[calc\(100\%_-_80px\)\]{
  max-width: calc(100% - 80px);
}
.group:hover .group-\[\:hover\]\:rotate-0{
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-\[\:hover\]\:scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group.simplebar-scrollable-x .group-\[\.simplebar-scrollable-x\]\:justify-start{
  justify-content: flex-start;
}
.group.active .group-\[\.active\]\:border-primary-500{
  border-color: rgb(var(--colors-primary-500));
}
.group.active .group-\[\.active\]\:border-theme-border{
  --tw-border-opacity: 1;
  border-color: rgb(231 234 238 / var(--tw-border-opacity));
}
.group.active .group-\[\.active\]\:border-b-primary-500{
  border-bottom-color: rgb(var(--colors-primary-500));
}
.group.active .group-\[\.active\]\:border-b-theme-cardbg{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.group.active .group-\[\.active\]\:bg-primary{
  background-color: rgb(var(--colors-primary));
}
.group.active .group-\[\.active\]\:bg-primary-500{
  background-color: rgb(var(--colors-primary-500));
}
.group.active .group-\[\.active\]\:bg-secondary-500\/10{
  background-color: rgb(91 107 121 / 0.1);
}
.group.active .group-\[\.active\]\:bg-theme-cardbg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.group.active .group-\[\.active\]\:bg-white\/10{
  background-color: rgb(255 255 255 / 0.1);
}
.group.mini-mail-list .group-\[\.mini-mail-list\]\:p-1{
  padding: 0.25rem;
}
.group.mini-mail-list .group-\[\.mini-mail-list\]\:text-\[16px\]{
  font-size: 16px;
}
.group.active .group-\[\.active\]\:text-primary-500{
  color: rgb(var(--colors-primary-500));
}
.group.active .group-\[\.active\]\:text-theme-bodycolor{
  --tw-text-opacity: 1;
  color: rgb(19 25 32 / var(--tw-text-opacity));
}
.group.active .group-\[\.active\]\:text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.group.active .group-\[\.active\]\:shadow-\[0px_8px_24px_rgba\(27\2c 46\2c 94\2c 0\.08\)\]{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.08);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group.mini-mail-list .group-\[\.mini-mail-list\]\:\*\:py-\[0\.4rem\] > *{
  padding-top: 0.4rem;
  padding-bottom: 0.4rem;
}
.peer\/component-list[open] ~ .peer-open\/component-list\:block{
  display: block;
}
.peer\/filecheck:checked ~ .peer-checked\/filecheck\:block{
  display: block;
}
.peer\/filecheck:checked ~ .peer-checked\/filecheck\:hidden{
  display: none;
}
.peer:checked ~ .peer-checked\:bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}
.peer\/prodlike:checked ~ .peer-checked\/prodlike\:fill-danger-500{
  fill: #dc2626;
}
.peer\/prodlike:checked ~ .peer-checked\/prodlike\:stroke-danger-500{
  stroke: #dc2626;
}
.peer:checked ~ .peer-checked\:after\:translate-x-full::after{
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer:checked ~ .peer-checked\:after\:border-white::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.peer:focus ~ .peer-focus\:outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.peer:focus ~ .peer-focus\:ring-4{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.peer:focus ~ .peer-focus\:ring-blue-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));
}
.has-\[\.show\]\:right-0:has(.show){
  right: 0px;
}
.has-\[\:checked\]\:bg-danger-500:has(:checked){
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
.has-\[\:checked\]\:bg-danger-500\/40:has(:checked){
  background-color: rgb(220 38 38 / 0.4);
}
.has-\[\:checked\]\:bg-dark-500\/40:has(:checked){
  background-color: rgb(33 37 41 / 0.4);
}
.has-\[\:checked\]\:bg-info-500\/40:has(:checked){
  background-color: rgb(62 201 214 / 0.4);
}
.has-\[\:checked\]\:bg-primary:has(:checked){
  background-color: rgb(var(--colors-primary));
}
.has-\[\:checked\]\:bg-primary-500:has(:checked){
  background-color: rgb(var(--colors-primary-500));
}
.has-\[\:checked\]\:bg-primary-500\/10:has(:checked){
  background-color: rgb(var(--colors-primary-500) / 0.1);
}
.has-\[\:checked\]\:bg-primary-500\/40:has(:checked){
  background-color: rgb(var(--colors-primary-500) / 0.4);
}
.has-\[\:checked\]\:bg-secondary-500:has(:checked){
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.has-\[\:checked\]\:bg-secondary-500\/40:has(:checked){
  background-color: rgb(91 107 121 / 0.4);
}
.has-\[\:checked\]\:bg-success-500:has(:checked){
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
}
.has-\[\:checked\]\:bg-success-500\/40:has(:checked){
  background-color: rgb(44 168 127 / 0.4);
}
.has-\[\:checked\]\:bg-warning-500\/40:has(:checked){
  background-color: rgb(229 138 0 / 0.4);
}
.has-\[\:checked\]\:text-white:has(:checked){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.has-\[\:checked\]\:shadow-\[inset_0_0_0_2px_rgba\(0\2c 0\2c 0\2c \.5\)\]:has(:checked){
  --tw-shadow: inset 0 0 0 2px rgba(0,0,0,.5);
  --tw-shadow-colored: inset 0 0 0 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.has-\[\:checked\]\:shadow-primary-500:has(:checked){
  --tw-shadow-color: rgb(var(--colors-primary-500));
  --tw-shadow: var(--tw-shadow-colored);
}
.group:has(:checked) .group-has-\[\:checked\]\:invisible{
  visibility: hidden;
}
.group:has(:checked) .group-has-\[\:checked\]\:flex{
  display: flex;
}
.group:has(:checked) .group-has-\[\:checked\]\:border-primary-500{
  border-color: rgb(var(--colors-primary-500));
}
.group:has(:checked) .group-has-\[\:checked\]\:bg-primary-500\/\[\.03\]{
  background-color: rgb(var(--colors-primary-500) / .03);
}
.group:has(:checked) .group-has-\[\:checked\]\:bg-white\/10{
  background-color: rgb(255 255 255 / 0.1);
}
.group:has(:checked) .group-has-\[\:checked\]\:opacity-0{
  opacity: 0;
}
.dark\:hidden:is([data-pc-theme="dark"] *){
  display: none;
}
.dark\:divide-themedark-border:is([data-pc-theme="dark"] *) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-divide-opacity));
}
.dark\:border-0:is([data-pc-theme="dark"] *){
  border-width: 0px;
}
.dark\:border-r:is([data-pc-theme="dark"] *){
  border-right-width: 1px;
}
.dark\:border-gray-600:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}
.dark\:border-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:border-themedark-cardbg:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(27 35 45 / var(--tw-border-opacity));
}
.dark\:border-themedark-inputborder:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:border-themedark-sidebarbordercolor:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(36 45 57 / var(--tw-border-opacity));
}
.dark\:border-white\/50:is([data-pc-theme="dark"] *){
  border-color: rgb(255 255 255 / 0.5);
}
.dark\:border-y-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(48 63 80 / var(--tw-border-opacity));
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:border-b-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-bottom-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.dark\:border-b-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-bottom-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:border-l-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-left-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:border-l-transparent:is([data-pc-theme="dark"] *){
  border-left-color: transparent;
}
.dark\:border-r-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-right-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:border-t-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.dark\:border-t-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:\!bg-themedark-inputbg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1 !important;
  background-color: rgb(38 50 64 / var(--tw-bg-opacity)) !important;
}
.dark\:\!bg-transparent:is([data-pc-theme="dark"] *){
  background-color: transparent !important;
}
.dark\:bg-\[\#303f50\]:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(48 63 80 / var(--tw-bg-opacity));
}
.dark\:bg-\[rgba\(0\2c 0\2c 0\2c 0\.2\)\]:is([data-pc-theme="dark"] *){
  background-color: rgba(0,0,0,0.2);
}
.dark\:bg-danger-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(220 38 38 / 0.1);
}
.dark\:bg-dark-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(33 37 41 / 0.1);
}
.dark\:bg-gray-200\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(229 231 235 / 0.1);
}
.dark\:bg-gray-700:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
.dark\:bg-info-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(62 201 214 / 0.1);
}
.dark\:bg-primary-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(var(--colors-primary-500) / 0.1);
}
.dark\:bg-secondary-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.dark\:bg-success-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(44 168 127 / 0.1);
}
.dark\:bg-themedark-bodybg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 25 32 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(191 191 191 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-border:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(48 63 80 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-cardbg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-headerbg:is([data-pc-theme="dark"] *){
  background-color: rgba( 19, 25, 32, 0.5);
}
.dark\:bg-themedark-horizontalsubmenubg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(38 50 64 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-inputbg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(38 50 64 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-sidebaruserbg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.dark\:bg-transparent:is([data-pc-theme="dark"] *){
  background-color: transparent;
}
.dark\:bg-warning-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(229 138 0 / 0.1);
}
.dark\:bg-white\/50:is([data-pc-theme="dark"] *){
  background-color: rgb(255 255 255 / 0.5);
}
.dark\:bg-opacity-75:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 0.75;
}
.dark\:bg-\[url\(\.\.\/images\/landing\/img-headerbg-dark\.jpg\)\]:is([data-pc-theme="dark"] *){
  background-image: url(../images/landing/img-headerbg-dark.jpg);
}
.dark\:bg-none:is([data-pc-theme="dark"] *){
  background-image: none;
}
.dark\:bg-select-bg-dark:is([data-pc-theme="dark"] *){
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23bfbfbf' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
.dark\:\!fill-themedark-bodycolor:is([data-pc-theme="dark"] *){
  fill: #bfbfbf !important;
}
.dark\:\!fill-themedark-inputbg:is([data-pc-theme="dark"] *){
  fill: #263240 !important;
}
.dark\:stroke-themedark-bodycolor:is([data-pc-theme="dark"] *){
  stroke: #bfbfbf;
}
.dark\:\!text-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
.dark\:text-dark-200:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(158 167 177 / var(--tw-text-opacity));
}
.dark\:text-secondary-200:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(190 198 206 / var(--tw-text-opacity));
}
.dark\:text-slate-200:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.dark\:text-slate-400:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.dark\:text-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.dark\:text-themedark-bodycolor\/50:is([data-pc-theme="dark"] *){
  color: rgb(191 191 191 / 0.5);
}
.dark\:text-themedark-bodycolor\/70:is([data-pc-theme="dark"] *){
  color: rgb(191 191 191 / 0.7);
}
.dark\:text-themedark-headercolor:is([data-pc-theme="dark"] *){
  color: rgba(255, 255, 255, 0.8);
}
.dark\:text-themedark-headings:is([data-pc-theme="dark"] *){
  color: rgba(255, 255, 255, 0.8);
}
.dark\:text-themedark-horizontalsubmenucolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.dark\:text-themedark-secondarytextcolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(116 136 146 / var(--tw-text-opacity));
}
.dark\:text-themedark-sidebarcolor:is([data-pc-theme="dark"] *){
  color: rgba(255, 255, 255, 0.5);
}
.dark\:text-white:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dark\:text-white\/50:is([data-pc-theme="dark"] *){
  color: rgb(255 255 255 / 0.5);
}
.dark\:text-white\/80:is([data-pc-theme="dark"] *){
  color: rgb(255 255 255 / 0.8);
}
.dark\:text-opacity-75:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 0.75;
}
.dark\:shadow-\[0_0_24px_rgba\(27\2c 46\2c 94\2c \.05\)\]:is([data-pc-theme="dark"] *){
  --tw-shadow: 0 0 24px rgba(27,46,94,.05);
  --tw-shadow-colored: 0 0 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark\:shadow-none:is([data-pc-theme="dark"] *){
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark\:shadow-themedark-border:is([data-pc-theme="dark"] *){
  --tw-shadow-color: #303f50;
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-themedark-cardbg:is([data-pc-theme="dark"] *){
  --tw-shadow-color: #1b232d;
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:\!outline-themedark-border:is([data-pc-theme="dark"] *){
  outline-color: #303f50 !important;
}
.dark\:ring-1:is([data-pc-theme="dark"] *){
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.dark\:ring-inset:is([data-pc-theme="dark"] *){
  --tw-ring-inset: inset;
}
.dark\:ring-white\/10:is([data-pc-theme="dark"] *){
  --tw-ring-color: rgb(255 255 255 / 0.1);
}
.\*\:dark\:border-themedark-border:is([data-pc-theme="dark"] *) > *{
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:\*\:border-themedark-border > *:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:\*\:bg-themedark-cardbg > *:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.dark\:\*\:text-themedark-bodycolor > *:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.dark\:before\:bg-themedark-cardbg:is([data-pc-theme="dark"] *)::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.dark\:after\:border-\[\#bfbfbf_transparent_transparent\]:is([data-pc-theme="dark"] *)::after{
  content: var(--tw-content);
  border-color: #bfbfbf transparent transparent;
}
.dark\:after\:bg-themedark-activebg:is([data-pc-theme="dark"] *)::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(25 33 42 / var(--tw-bg-opacity));
}
.dark\:after\:bg-themedark-cardbg:is([data-pc-theme="dark"] *)::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.dark\:open\:\*\:\*\:\!bg-primary-500\/10 > * > *[open]:is([data-pc-theme="dark"] *){
  background-color: rgb(var(--colors-primary-500) / 0.1) !important;
}
.dark\:hover\:border-primary-500:hover:is([data-pc-theme="dark"] *){
  border-color: rgb(var(--colors-primary-500));
}
.dark\:hover\:border-themedark-border:hover:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.dark\:hover\:bg-danger-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-dark-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-dark-500\/10:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(33 37 41 / 0.1);
}
.dark\:hover\:bg-info-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(62 201 214 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-primary-500:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(var(--colors-primary-500));
}
.dark\:hover\:bg-secondary-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-secondary-500\/10:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.dark\:hover\:bg-success-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(44 168 127 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-themedark-activebg:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(25 33 42 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-themedark-sidebarcolor\/20:hover:is([data-pc-theme="dark"] *){
  background-color: rgba(255, 255, 255, 0.2);
}
.dark\:hover\:bg-warning-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(229 138 0 / var(--tw-bg-opacity));
}
.dark\:hover\:text-primary-500:hover:is([data-pc-theme="dark"] *){
  color: rgb(var(--colors-primary-500));
}
.dark\:hover\:text-themedark-bodycolor:hover:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.dark\:hover\:text-opacity-100:hover:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
}
.dark\:hover\:\*\:text-primary-500 > *:hover:is([data-pc-theme="dark"] *){
  color: rgb(var(--colors-primary-500));
}
.dark\:focus\:border-primary-500:focus:is([data-pc-theme="dark"] *){
  border-color: rgb(var(--colors-primary-500));
}
.dark\:focus\:bg-secondary-500\/10:focus:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.group.active .dark\:group-\[\.active\]\:border-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(48 63 80 / var(--tw-border-opacity));
}
.group.active .dark\:group-\[\.active\]\:border-b-themedark-cardbg:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-bottom-color: rgb(27 35 45 / var(--tw-border-opacity));
}
.group.active .group-\[\.active\]\:dark\:bg-themedark-cardbg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(27 35 45 / var(--tw-bg-opacity));
}
.group.active .dark\:group-\[\.active\]\:text-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.peer:focus ~ .dark\:peer-focus\:ring-blue-800:is([data-pc-theme="dark"] *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity));
}
@media not all and (min-width: 1536px){
  .max-2xl\:\!rounded-none{
    border-radius: 0px !important;
  }
}
@media not all and (min-width: 1280px){
  .max-xl\:m-0{
    margin: 0px;
  }
  .max-xl\:h-full{
    height: 100%;
  }
  .max-xl\:w-full{
    width: 100%;
  }
  .max-xl\:max-w-none{
    max-width: none;
  }
  .max-xl\:overflow-y-auto{
    overflow-y: auto;
  }
  .max-xl\:rounded-none{
    border-radius: 0px;
  }
}
@media not all and (min-width: 1024px){
  .max-lg\:-left-sidebar-width{
    left: -280px;
  }
  .max-lg\:left-0{
    left: 0px;
  }
  .max-lg\:m-0{
    margin: 0px;
  }
  .max-lg\:ml-0{
    margin-left: 0px;
  }
  .max-lg\:hidden{
    display: none;
  }
  .max-lg\:h-full{
    height: 100%;
  }
  .max-lg\:w-full{
    width: 100%;
  }
  .max-lg\:max-w-none{
    max-width: none;
  }
  .max-lg\:overflow-y-auto{
    overflow-y: auto;
  }
  .max-lg\:rounded-none{
    border-radius: 0px;
  }
  .max-lg\:py-0{
    padding-top: 0px;
    padding-bottom: 0px;
  }
}
@media not all and (min-width: 768px){
  .max-md\:float-none{
    float: none;
  }
  .max-md\:m-0{
    margin: 0px;
  }
  .max-md\:mx-0{
    margin-left: 0px;
    margin-right: 0px;
  }
  .max-md\:my-2\.5{
    margin-top: 0.625rem;
    margin-bottom: 0.625rem;
  }
  .max-md\:block{
    display: block;
  }
  .max-md\:hidden{
    display: none;
  }
  .max-md\:h-full{
    height: 100%;
  }
  .max-md\:w-full{
    width: 100%;
  }
  .max-md\:max-w-none{
    max-width: none;
  }
  .max-md\:flex-col{
    flex-direction: column;
  }
  .max-md\:gap-4{
    gap: 1rem;
  }
  .max-md\:overflow-y-auto{
    overflow-y: auto;
  }
  .max-md\:rounded-none{
    border-radius: 0px;
  }
  .max-md\:text-center{
    text-align: center;
  }
  .max-md\:\*\:mx-auto > *{
    margin-left: auto;
    margin-right: auto;
  }
  .max-md\:\*\:mb-2 > *{
    margin-bottom: 0.5rem;
  }
}
@media not all and (min-width: 640px){
  .max-sm\:static{
    position: static;
  }
  .max-sm\:\!left-\[15px\]{
    left: 15px !important;
  }
  .max-sm\:\!right-\[15px\]{
    right: 15px !important;
  }
  .max-sm\:\!top-full{
    top: 100% !important;
  }
  .max-sm\:m-0{
    margin: 0px;
  }
  .max-sm\:mr-2{
    margin-right: 0.5rem;
  }
  .max-sm\:h-full{
    height: 100%;
  }
  .max-sm\:w-full{
    width: 100%;
  }
  .max-sm\:min-w-\[calc\(100vw_-_30px\)\]{
    min-width: calc(100vw - 30px);
  }
  .max-sm\:max-w-none{
    max-width: none;
  }
  .max-sm\:\!transform-none{
    transform: none !important;
  }
  .max-sm\:flex-col{
    flex-direction: column;
  }
  .max-sm\:items-start{
    align-items: flex-start;
  }
  .max-sm\:gap-2{
    gap: 0.5rem;
  }
  .max-sm\:overflow-y-auto{
    overflow-y: auto;
  }
  .max-sm\:rounded-full{
    border-radius: 9999px;
  }
  .max-sm\:rounded-none{
    border-radius: 0px;
  }
  .max-sm\:p-\[13px\]{
    padding: 13px;
  }
  .max-sm\:p-\[15px\]{
    padding: 15px;
  }
  .max-sm\:px-2\.5{
    padding-left: 0.625rem;
    padding-right: 0.625rem;
  }
  .max-sm\:px-\[15px\]{
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media (min-width: 640px){
  .sm\:relative{
    position: relative;
  }
  .sm\:col-span-10{
    grid-column: span 10 / span 10;
  }
  .sm\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .sm\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .sm\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .sm\:col-span-5{
    grid-column: span 5 / span 5;
  }
  .sm\:col-span-6{
    grid-column: span 6 / span 6;
  }
  .sm\:col-span-7{
    grid-column: span 7 / span 7;
  }
  .sm\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .sm\:col-span-9{
    grid-column: span 9 / span 9;
  }
  .sm\:col-start-4{
    grid-column-start: 4;
  }
  .sm\:my-12{
    margin-top: 3rem;
    margin-bottom: 3rem;
  }
  .sm\:mb-0{
    margin-bottom: 0px;
  }
  .sm\:mb-5{
    margin-bottom: 1.25rem;
  }
  .sm\:ml-0{
    margin-left: 0px;
  }
  .sm\:ml-2{
    margin-left: 0.5rem;
  }
  .sm\:ms-0{
    margin-inline-start: 0px;
  }
  .sm\:mt-2{
    margin-top: 0.5rem;
  }
  .sm\:mt-5{
    margin-top: 1.25rem;
  }
  .sm\:block{
    display: block;
  }
  .sm\:inline-block{
    display: inline-block;
  }
  .sm\:flex{
    display: flex;
  }
  .sm\:inline-flex{
    display: inline-flex;
  }
  .sm\:hidden{
    display: none;
  }
  .sm\:w-2\/4{
    width: 50%;
  }
  .sm\:w-\[300px\]{
    width: 300px;
  }
  .sm\:w-full{
    width: 100%;
  }
  .sm\:min-w-\[352px\]{
    min-width: 352px;
  }
  .sm\:min-w-\[450px\]{
    min-width: 450px;
  }
  .sm\:shrink-0{
    flex-shrink: 0;
  }
  .sm\:grid-cols-1{
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .sm\:grid-cols-11{
    grid-template-columns: repeat(11, minmax(0, 1fr));
  }
  .sm\:flex-row{
    flex-direction: row;
  }
  .sm\:items-stretch{
    align-items: stretch;
  }
  .sm\:justify-between{
    justify-content: space-between;
  }
  .sm\:space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
  }
  .sm\:object-center{
    -o-object-position: center;
       object-position: center;
  }
  .sm\:\!p-10{
    padding: 2.5rem !important;
  }
  .sm\:p-10{
    padding: 2.5rem;
  }
  .sm\:px-\[25px\]{
    padding-left: 25px;
    padding-right: 25px;
  }
  .sm\:py-\[100px\]{
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .sm\:pb-5{
    padding-bottom: 1.25rem;
  }
  .sm\:pt-\[100px\]{
    padding-top: 100px;
  }
  .sm\:text-center{
    text-align: center;
  }
  .sm\:text-right{
    text-align: right;
  }
  .sm\:text-end{
    text-align: end;
  }
  .sm\:text-\[0\.625rem\]{
    font-size: 0.625rem;
  }
  .sm\:text-\[16px\]{
    font-size: 16px;
  }
}
@media (min-width: 768px){
  .md\:col-span-10{
    grid-column: span 10 / span 10;
  }
  .md\:col-span-11{
    grid-column: span 11 / span 11;
  }
  .md\:col-span-12{
    grid-column: span 12 / span 12;
  }
  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .md\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .md\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .md\:col-span-5{
    grid-column: span 5 / span 5;
  }
  .md\:col-span-6{
    grid-column: span 6 / span 6;
  }
  .md\:col-span-7{
    grid-column: span 7 / span 7;
  }
  .md\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .md\:col-span-9{
    grid-column: span 9 / span 9;
  }
  .md\:my-10{
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
  }
  .md\:mb-0{
    margin-bottom: 0px;
  }
  .md\:mt-5{
    margin-top: 1.25rem;
  }
  .md\:block{
    display: block;
  }
  .md\:inline-block{
    display: inline-block;
  }
  .md\:flex{
    display: flex;
  }
  .md\:inline-flex{
    display: inline-flex;
  }
  .md\:w-10\/12{
    width: 83.333333%;
  }
  .md\:w-4\/5{
    width: 80%;
  }
  .md\:w-8\/12{
    width: 66.666667%;
  }
  .md\:max-w-\[540px\]{
    max-width: 540px;
  }
  .md\:basis-10\/12{
    flex-basis: 83.333333%;
  }
  .md\:flex-row{
    flex-direction: row;
  }
  .md\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .md\:text-right{
    text-align: right;
  }
  .md\:text-start{
    text-align: start;
  }
  .md\:text-\[36px\]{
    font-size: 36px;
  }
  .md\:text-xs{
    font-size: 0.75rem;
    line-height: 1rem;
  }
}
@media (min-width: 1024px){
  .lg\:col-span-12{
    grid-column: span 12 / span 12;
  }
  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .lg\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .lg\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .lg\:col-span-5{
    grid-column: span 5 / span 5;
  }
  .lg\:col-span-6{
    grid-column: span 6 / span 6;
  }
  .lg\:col-span-7{
    grid-column: span 7 / span 7;
  }
  .lg\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .lg\:col-span-9{
    grid-column: span 9 / span 9;
  }
  .lg\:col-start-4{
    grid-column-start: 4;
  }
  .lg\:block{
    display: block;
  }
  .lg\:inline-flex{
    display: inline-flex;
  }
  .lg\:hidden{
    display: none;
  }
  .lg\:w-0{
    width: 0px;
  }
  .lg\:max-w-\[720px\]{
    max-width: 720px;
  }
  .lg\:max-w-\[960px\]{
    max-width: 960px;
  }
  .lg\:basis-8\/12{
    flex-basis: 66.666667%;
  }
  .lg\:gap-6{
    gap: 1.5rem;
  }
  .lg\:px-0{
    padding-left: 0px;
    padding-right: 0px;
  }
  .lg\:px-12{
    padding-left: 3rem;
    padding-right: 3rem;
  }
  .lg\:text-right{
    text-align: right;
  }
  .lg\:text-end{
    text-align: end;
  }
  .lg\:text-\[0\.625rem\]{
    font-size: 0.625rem;
  }
  .lg\:text-\[55px\]{
    font-size: 55px;
  }
}
@media (min-width: 1280px){
  .xl\:sticky{
    position: sticky;
  }
  .xl\:left-0{
    left: 0px;
  }
  .xl\:top-28{
    top: 7rem;
  }
  .xl\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .xl\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .xl\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .xl\:col-span-5{
    grid-column: span 5 / span 5;
  }
  .xl\:col-span-6{
    grid-column: span 6 / span 6;
  }
  .xl\:col-span-7{
    grid-column: span 7 / span 7;
  }
  .xl\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .xl\:col-span-9{
    grid-column: span 9 / span 9;
  }
  .xl\:hidden{
    display: none;
  }
  .xl\:h-\[calc\(100vh_-_235px\)\]{
    height: calc(100vh - 235px);
  }
  .xl\:w-6\/12{
    width: 50%;
  }
  .xl\:rounded-xl{
    border-radius: 0.75rem;
  }
  .xl\:pt-28{
    padding-top: 7rem;
  }
}
@media (min-width: 1536px){
  .\32xl\:visible{
    visibility: visible;
  }
  .\32xl\:relative{
    position: relative;
  }
  .\32xl\:\!left-0{
    left: 0px !important;
  }
  .\32xl\:\!right-0{
    right: 0px !important;
  }
  .\32xl\:\!z-10{
    z-index: 10 !important;
  }
  .\32xl\:col-span-12{
    grid-column: span 12 / span 12;
  }
  .\32xl\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .\32xl\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .\32xl\:col-span-6{
    grid-column: span 6 / span 6;
  }
  .\32xl\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .\32xl\:col-span-9{
    grid-column: span 9 / span 9;
  }
  .\32xl\:col-end-1{
    grid-column-end: 1;
  }
  .\32xl\:mt-0{
    margin-top: 0px;
  }
  .\32xl\:inline-flex{
    display: inline-flex;
  }
  .\32xl\:contents{
    display: contents;
  }
  .\32xl\:\!hidden{
    display: none !important;
  }
  .\32xl\:hidden{
    display: none;
  }
  .\32xl\:w-full{
    width: 100%;
  }
  .\32xl\:max-w-\[1140px\]{
    max-width: 1140px;
  }
  .\32xl\:bg-transparent{
    background-color: transparent;
  }
  .\32xl\:pt-2\.5{
    padding-top: 0.625rem;
  }
  .\32xl\:text-xs{
    font-size: 0.75rem;
    line-height: 1rem;
  }
  .\32xl\:shadow-none{
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .dark\:2xl\:bg-transparent:is([data-pc-theme="dark"] *){
    background-color: transparent;
  }
}
.ltr\:\!right-3:where([dir="ltr"], [dir="ltr"] *){
  right: 0.75rem !important;
}
.ltr\:-right-1:where([dir="ltr"], [dir="ltr"] *){
  right: -0.25rem;
}
.ltr\:left-3:where([dir="ltr"], [dir="ltr"] *){
  left: 0.75rem;
}
.ltr\:left-full:where([dir="ltr"], [dir="ltr"] *){
  left: 100%;
}
.ltr\:right-0:where([dir="ltr"], [dir="ltr"] *){
  right: 0px;
}
.ltr\:right-1:where([dir="ltr"], [dir="ltr"] *){
  right: 0.25rem;
}
.ltr\:right-1\.5:where([dir="ltr"], [dir="ltr"] *){
  right: 0.375rem;
}
.ltr\:right-2:where([dir="ltr"], [dir="ltr"] *){
  right: 0.5rem;
}
.ltr\:right-\[14px\]:where([dir="ltr"], [dir="ltr"] *){
  right: 14px;
}
.ltr\:right-px:where([dir="ltr"], [dir="ltr"] *){
  right: 1px;
}
.ltr\:float-right:where([dir="ltr"], [dir="ltr"] *){
  float: right;
}
.ltr\:\!ml-0:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px !important;
}
.ltr\:ml-0:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px;
}
.ltr\:ml-2:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0.5rem;
}
.ltr\:ml-3:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0.75rem;
}
.ltr\:ml-4:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 1rem;
}
.ltr\:ml-6:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 1.5rem;
}
.ltr\:ml-auto:where([dir="ltr"], [dir="ltr"] *){
  margin-left: auto;
}
.ltr\:mr-1:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 0.25rem;
}
.ltr\:mr-2:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 0.5rem;
}
.ltr\:mr-2\.5:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 0.625rem;
}
.ltr\:mr-3:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 0.75rem;
}
.ltr\:mr-6:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 1.5rem;
}
.ltr\:mr-\[1\.35rem\]:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 1.35rem;
}
.ltr\:mr-\[15px\]:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 15px;
}
.ltr\:mr-\[5px\]:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 5px;
}
.ltr\:mr-auto:where([dir="ltr"], [dir="ltr"] *){
  margin-right: auto;
}
.ltr\:origin-left:where([dir="ltr"], [dir="ltr"] *){
  transform-origin: left;
}
.ltr\:origin-right:where([dir="ltr"], [dir="ltr"] *){
  transform-origin: right;
}
.ltr\:origin-top-left:where([dir="ltr"], [dir="ltr"] *){
  transform-origin: top left;
}
.ltr\:-translate-x-1\/2:where([dir="ltr"], [dir="ltr"] *){
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.ltr\:rounded-\[50\%_4px_4px_50\%\]:where([dir="ltr"], [dir="ltr"] *){
  border-radius: 50% 4px 4px 50%;
}
.ltr\:rounded-l-lg:where([dir="ltr"], [dir="ltr"] *){
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.ltr\:rounded-l-none:where([dir="ltr"], [dir="ltr"] *){
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.ltr\:rounded-r-lg:where([dir="ltr"], [dir="ltr"] *){
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.ltr\:rounded-r-none:where([dir="ltr"], [dir="ltr"] *){
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.ltr\:rounded-tl-xl:where([dir="ltr"], [dir="ltr"] *){
  border-top-left-radius: 0.75rem;
}
.ltr\:rounded-tr-xl:where([dir="ltr"], [dir="ltr"] *){
  border-top-right-radius: 0.75rem;
}
.ltr\:border-r:where([dir="ltr"], [dir="ltr"] *){
  border-right-width: 1px;
}
.ltr\:border-r-0:where([dir="ltr"], [dir="ltr"] *){
  border-right-width: 0px;
}
.ltr\:bg-\[right_1rem_center\]:where([dir="ltr"], [dir="ltr"] *){
  background-position: right 1rem center;
}
.ltr\:\!pr-\[17px\]:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 17px !important;
}
.ltr\:pl-20:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 5rem;
}
.ltr\:pl-4:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 1rem;
}
.ltr\:pl-8:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 2rem;
}
.ltr\:pl-\[60px\]:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 60px;
}
.ltr\:pl-\[95px\]:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 95px;
}
.ltr\:pr-10:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 2.5rem;
}
.ltr\:pr-4:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 1rem;
}
.ltr\:pr-8:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 2rem;
}
.ltr\:text-left:where([dir="ltr"], [dir="ltr"] *){
  text-align: left;
}
.ltr\:\!text-right:where([dir="ltr"], [dir="ltr"] *){
  text-align: right !important;
}
.ltr\:text-right:where([dir="ltr"], [dir="ltr"] *){
  text-align: right;
}
.ltr\:\*\:-ml-px > *:where([dir="ltr"], [dir="ltr"] *){
  margin-left: -1px;
}
.ltr\:before\:right-\[-20px\]:where([dir="ltr"], [dir="ltr"] *)::before{
  content: var(--tw-content);
  right: -20px;
}
.ltr\:before\:right-\[25px\]:where([dir="ltr"], [dir="ltr"] *)::before{
  content: var(--tw-content);
  right: 25px;
}
.ltr\:before\:content-\[\'\\\\ea61\'\]:where([dir="ltr"], [dir="ltr"] *)::before{
  --tw-content: '\\ea61';
  content: var(--tw-content);
}
.ltr\:after\:left-7:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 1.75rem;
}
.ltr\:after\:left-\[62px\]:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 62px;
}
.ltr\:after\:left-\[79px\]:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 79px;
}
.ltr\:first\:pl-5:first-child:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 1.25rem;
}
.ltr\:first\:\*\:ml-0 > *:first-child:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px;
}
.ltr\:first\:\*\:rounded-l-lg > *:first-child:where([dir="ltr"], [dir="ltr"] *){
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.ltr\:last\:pr-5:last-child:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 1.25rem;
}
.ltr\:last\:\*\:rounded-r-lg > *:last-child:where([dir="ltr"], [dir="ltr"] *){
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
@media not all and (min-width: 1536px){
  .ltr\:max-2xl\:ml-6:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 1.5rem;
  }
  .ltr\:max-2xl\:mr-0:where([dir="ltr"], [dir="ltr"] *){
    margin-right: 0px;
  }
  .ltr\:max-2xl\:mr-6:where([dir="ltr"], [dir="ltr"] *){
    margin-right: 1.5rem;
  }
}
@media not all and (min-width: 1024px){
  .ltr\:max-lg\:left-0:where([dir="ltr"], [dir="ltr"] *){
    left: 0px;
  }
}
@media (min-width: 640px){
  .ltr\:sm\:text-right:where([dir="ltr"], [dir="ltr"] *){
    text-align: right;
  }
  .ltr\:first\:sm\:pl-\[25px\]:first-child:where([dir="ltr"], [dir="ltr"] *){
    padding-left: 25px;
  }
  .ltr\:first\:sm\:pr-2:first-child:where([dir="ltr"], [dir="ltr"] *){
    padding-right: 0.5rem;
  }
  .ltr\:last\:sm\:pl-2:last-child:where([dir="ltr"], [dir="ltr"] *){
    padding-left: 0.5rem;
  }
  .ltr\:last\:sm\:pr-\[25px\]:last-child:where([dir="ltr"], [dir="ltr"] *){
    padding-right: 25px;
  }
}
@media (min-width: 1024px){
  .ltr\:lg\:left-0:where([dir="ltr"], [dir="ltr"] *){
    left: 0px;
  }
  .ltr\:lg\:left-sidebar-width:where([dir="ltr"], [dir="ltr"] *){
    left: 280px;
  }
  .ltr\:lg\:ml-0:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 0px;
  }
  .ltr\:lg\:ml-sidebar-width:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 280px;
  }
}
.rtl\:\!left-3:where([dir="rtl"], [dir="rtl"] *){
  left: 0.75rem !important;
}
.rtl\:\!left-auto:where([dir="rtl"], [dir="rtl"] *){
  left: auto !important;
}
.rtl\:\!right-0:where([dir="rtl"], [dir="rtl"] *){
  right: 0px !important;
}
.rtl\:\!right-auto:where([dir="rtl"], [dir="rtl"] *){
  right: auto !important;
}
.rtl\:-left-1:where([dir="rtl"], [dir="rtl"] *){
  left: -0.25rem;
}
.rtl\:left-0:where([dir="rtl"], [dir="rtl"] *){
  left: 0px;
}
.rtl\:left-1:where([dir="rtl"], [dir="rtl"] *){
  left: 0.25rem;
}
.rtl\:left-1\.5:where([dir="rtl"], [dir="rtl"] *){
  left: 0.375rem;
}
.rtl\:left-2:where([dir="rtl"], [dir="rtl"] *){
  left: 0.5rem;
}
.rtl\:left-\[14px\]:where([dir="rtl"], [dir="rtl"] *){
  left: 14px;
}
.rtl\:left-px:where([dir="rtl"], [dir="rtl"] *){
  left: 1px;
}
.rtl\:right-3:where([dir="rtl"], [dir="rtl"] *){
  right: 0.75rem;
}
.rtl\:right-auto:where([dir="rtl"], [dir="rtl"] *){
  right: auto;
}
.rtl\:right-full:where([dir="rtl"], [dir="rtl"] *){
  right: 100%;
}
.rtl\:float-left:where([dir="rtl"], [dir="rtl"] *){
  float: left;
}
.rtl\:\!mr-0:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px !important;
}
.rtl\:ml-1:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 0.25rem;
}
.rtl\:ml-2:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 0.5rem;
}
.rtl\:ml-2\.5:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 0.625rem;
}
.rtl\:ml-3:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 0.75rem;
}
.rtl\:ml-6:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 1.5rem;
}
.rtl\:ml-\[1\.35rem\]:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 1.35rem;
}
.rtl\:ml-\[15px\]:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 15px;
}
.rtl\:ml-\[5px\]:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 5px;
}
.rtl\:ml-auto:where([dir="rtl"], [dir="rtl"] *){
  margin-left: auto;
}
.rtl\:mr-0:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px;
}
.rtl\:mr-2:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0.5rem;
}
.rtl\:mr-3:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0.75rem;
}
.rtl\:mr-4:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 1rem;
}
.rtl\:mr-6:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 1.5rem;
}
.rtl\:mr-auto:where([dir="rtl"], [dir="rtl"] *){
  margin-right: auto;
}
.rtl\:origin-top-right:where([dir="rtl"], [dir="rtl"] *){
  transform-origin: top right;
}
.rtl\:translate-x-1\/2:where([dir="rtl"], [dir="rtl"] *){
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rtl\:divide-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-x-reverse: 1;
}
.rtl\:rounded-\[4px_50\%_50\%_4px\]:where([dir="rtl"], [dir="rtl"] *){
  border-radius: 4px 50% 50% 4px;
}
.rtl\:rounded-l-lg:where([dir="rtl"], [dir="rtl"] *){
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rtl\:rounded-l-none:where([dir="rtl"], [dir="rtl"] *){
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.rtl\:rounded-r-lg:where([dir="rtl"], [dir="rtl"] *){
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rtl\:rounded-r-none:where([dir="rtl"], [dir="rtl"] *){
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.rtl\:rounded-tl-xl:where([dir="rtl"], [dir="rtl"] *){
  border-top-left-radius: 0.75rem;
}
.rtl\:rounded-tr-xl:where([dir="rtl"], [dir="rtl"] *){
  border-top-right-radius: 0.75rem;
}
.rtl\:border-l:where([dir="rtl"], [dir="rtl"] *){
  border-left-width: 1px;
}
.rtl\:border-l-0:where([dir="rtl"], [dir="rtl"] *){
  border-left-width: 0px;
}
.rtl\:bg-\[left_1rem_center\]:where([dir="rtl"], [dir="rtl"] *){
  background-position: left 1rem center;
}
.rtl\:\!pl-8:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 2rem !important;
}
.rtl\:\!pl-\[17px\]:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 17px !important;
}
.rtl\:\!pr-0:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 0px !important;
}
.rtl\:\!pr-\[\.75rem\]:where([dir="rtl"], [dir="rtl"] *){
  padding-right: .75rem !important;
}
.rtl\:pl-10:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 2.5rem;
}
.rtl\:pl-12:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 3rem;
}
.rtl\:pl-4:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 1rem;
}
.rtl\:pr-20:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 5rem;
}
.rtl\:pr-4:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 1rem;
}
.rtl\:pr-5:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 1.25rem;
}
.rtl\:pr-8:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 2rem;
}
.rtl\:pr-\[60px\]:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 60px;
}
.rtl\:pr-\[95px\]:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 95px;
}
.rtl\:\!text-left:where([dir="rtl"], [dir="rtl"] *){
  text-align: left !important;
}
.rtl\:text-left:where([dir="rtl"], [dir="rtl"] *){
  text-align: left;
}
.rtl\:text-right:where([dir="rtl"], [dir="rtl"] *){
  text-align: right;
}
.rtl\:\*\:-mr-px > *:where([dir="rtl"], [dir="rtl"] *){
  margin-right: -1px;
}
.rtl\:before\:left-\[-20px\]:where([dir="rtl"], [dir="rtl"] *)::before{
  content: var(--tw-content);
  left: -20px;
}
.rtl\:before\:left-\[25px\]:where([dir="rtl"], [dir="rtl"] *)::before{
  content: var(--tw-content);
  left: 25px;
}
.rtl\:before\:content-\[\'\\\\ea60\'\]:where([dir="rtl"], [dir="rtl"] *)::before{
  --tw-content: '\\ea60';
  content: var(--tw-content);
}
.rtl\:after\:right-7:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 1.75rem;
}
.rtl\:after\:right-\[62px\]:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 62px;
}
.rtl\:after\:right-\[79px\]:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 79px;
}
.rtl\:first\:pr-5:first-child:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 1.25rem;
}
.rtl\:first\:\*\:mr-0 > *:first-child:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px;
}
.rtl\:first\:\*\:rounded-r-lg > *:first-child:where([dir="rtl"], [dir="rtl"] *){
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rtl\:last\:pl-5:last-child:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 1.25rem;
}
.rtl\:last\:\*\:rounded-l-lg > *:last-child:where([dir="rtl"], [dir="rtl"] *){
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.peer:checked ~ .rtl\:peer-checked\:after\:-translate-x-full:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@media not all and (min-width: 1536px){
  .rtl\:max-2xl\:ml-0:where([dir="rtl"], [dir="rtl"] *){
    margin-left: 0px;
  }
  .rtl\:max-2xl\:ml-6:where([dir="rtl"], [dir="rtl"] *){
    margin-left: 1.5rem;
  }
  .rtl\:max-2xl\:mr-6:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 1.5rem;
  }
}
@media not all and (min-width: 1024px){
  .rtl\:max-lg\:right-0:where([dir="rtl"], [dir="rtl"] *){
    right: 0px;
  }
}
@media (min-width: 640px){
  .rtl\:sm\:text-left:where([dir="rtl"], [dir="rtl"] *){
    text-align: left;
  }
  .rtl\:first\:sm\:pl-2:first-child:where([dir="rtl"], [dir="rtl"] *){
    padding-left: 0.5rem;
  }
  .rtl\:first\:sm\:pr-\[25px\]:first-child:where([dir="rtl"], [dir="rtl"] *){
    padding-right: 25px;
  }
  .rtl\:last\:sm\:pl-\[25px\]:last-child:where([dir="rtl"], [dir="rtl"] *){
    padding-left: 25px;
  }
  .rtl\:last\:sm\:pr-2:last-child:where([dir="rtl"], [dir="rtl"] *){
    padding-right: 0.5rem;
  }
}
@media (min-width: 1024px){
  .rtl\:lg\:right-0:where([dir="rtl"], [dir="rtl"] *){
    right: 0px;
  }
  .rtl\:lg\:right-sidebar-width:where([dir="rtl"], [dir="rtl"] *){
    right: 280px;
  }
  .rtl\:lg\:mr-0:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 0px;
  }
  .rtl\:lg\:mr-sidebar-width:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 280px;
  }
}
@media (forced-colors: active){
  .forced-colors\:appearance-auto{
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}
@media print{
  .print\:hidden{
    display: none;
  }
}
.\[\&\:not\(\.show\)\]\:right-\[-550px\]:not(.show){
  right: -550px;
}
.\[\&\>\*\:nth-child\(1\)\]\:w-\[130px\]>*:nth-child(1){
  width: 130px;
}
.\[\&\>\*\:nth-child\(2\)\]\:w-\[180px\]>*:nth-child(2){
  width: 180px;
}
.\[\&\>\*\:nth-child\(3\)\]\:min-w-0>*:nth-child(3){
  min-width: 0px;
}
.\[\&\>\*\:nth-child\(3\)\]\:flex-auto>*:nth-child(3){
  flex: 1 1 auto;
}
.\[\&\>\*\:nth-child\(4\)\]\:w-\[65px\]>*:nth-child(4){
  width: 65px;
}
.\[\&\>\*\:nth-child\(5\)\]\:w-\[150px\]>*:nth-child(5){
  width: 150px;
}